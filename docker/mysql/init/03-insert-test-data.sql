-- 插入测试数据

-- 认证服务测试数据
USE `auth_db`;

-- 插入系统角色
INSERT INTO `sys_role` (`role_code`, `role_name`, `description`, `create_by`) VALUES
('ADMIN', '系统管理员', '系统管理员角色，拥有所有权限', 'system'),
('FINANCE_MANAGER', '财务经理', '财务经理角色，负责财务管理和审批', 'system'),
('FINANCE_STAFF', '财务专员', '财务专员角色，负责日常财务操作', 'system'),
('SUPPLIER', '供应商用户', '供应商用户角色，查看自己的相关信息', 'system');

-- 插入系统用户 (密码都是 123456，使用BCrypt加密)
INSERT INTO `sys_user` (`user_code`, `username`, `password`, `real_name`, `email`, `phone`, `create_by`) VALUES
('U001', 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKTcQCkdQTcQbqrxcCNdHsw6cISq', '系统管理员', '<EMAIL>', '13800138000', 'system'),
('U002', 'finance_manager', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKTcQCkdQTcQbqrxcCNdHsw6cISq', '财务经理', '<EMAIL>', '13800138001', 'system'),
('U003', 'finance_staff', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKTcQCkdQTcQbqrxcCNdHsw6cISq', '财务专员', '<EMAIL>', '13800138002', 'system'),
('U004', 'supplier_user', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKTcQCkdQTcQbqrxcCNdHsw6cISq', '供应商用户', '<EMAIL>', '***********', 'system');

-- 插入用户角色关联
INSERT INTO `sys_user_role` (`user_id`, `role_id`) VALUES
(1, 1), -- admin -> ADMIN
(2, 2), -- finance_manager -> FINANCE_MANAGER
(3, 3), -- finance_staff -> FINANCE_STAFF
(4, 4); -- supplier_user -> SUPPLIER

-- 结算服务测试数据
USE `settlement_db`;

-- 插入供应商信息
INSERT INTO `supplier_info` (`supplier_code`, `supplier_name`, `contact_person`, `contact_phone`, `contact_email`, `bank_account`, `bank_name`, `settlement_cycle`, `create_by`) VALUES
('SUP001', '北京科技有限公司', '张三', '***********', '<EMAIL>', '****************', '中国银行北京分行', 30, 'system'),
('SUP002', '上海贸易有限公司', '李四', '***********', '<EMAIL>', '****************', '工商银行上海分行', 30, 'system'),
('SUP003', '深圳制造有限公司', '王五', '***********', '<EMAIL>', '****************', '建设银行深圳分行', 15, 'system'),
('SUP004', '广州服务有限公司', '赵六', '***********', '<EMAIL>', '****************', '农业银行广州分行', 45, 'system');

-- 单据处理服务测试数据
USE `document_db`;

-- 插入业务单据
INSERT INTO `business_document` (`document_no`, `document_type`, `supplier_id`, `organization_id`, `amount`, `business_date`, `source_system`, `remark`, `create_by`) VALUES
('DOC202401001', 'PURCHASE', 1, 1, 50000.00, '2024-01-15', 'ERP', '采购办公用品', 'system'),
('DOC202401002', 'SERVICE', 2, 1, 30000.00, '2024-01-16', 'ERP', '技术服务费', 'system'),
('DOC202401003', 'PURCHASE', 3, 1, 80000.00, '2024-01-17', 'ERP', '采购设备', 'system'),
('DOC202401004', 'SERVICE', 4, 1, 25000.00, '2024-01-18', 'ERP', '咨询服务费', 'system'),
('DOC202401005', 'PURCHASE', 1, 1, 35000.00, '2024-01-19', 'ERP', '采购原材料', 'system');

-- 插入单据明细
INSERT INTO `document_detail` (`document_id`, `item_code`, `item_name`, `quantity`, `unit_price`, `amount`, `unit`) VALUES
(1, 'ITEM001', '办公桌', 10, 2000.00, 20000.00, '张'),
(1, 'ITEM002', '办公椅', 20, 1500.00, 30000.00, '把'),
(2, 'SERV001', '系统开发', 1, 30000.00, 30000.00, '项'),
(3, 'EQUIP001', '服务器', 2, 40000.00, 80000.00, '台'),
(4, 'CONS001', '管理咨询', 1, 25000.00, 25000.00, '项'),
(5, 'MAT001', '钢材', 100, 350.00, 35000.00, '吨');

-- 支付服务测试数据
USE `payment_db`;

-- 插入支付订单
INSERT INTO `payment_order` (`order_no`, `supplier_id`, `payment_amount`, `payment_method`, `bank_account`, `bank_name`, `remark`, `create_by`) VALUES
('PAY202401001', 1, 50000.00, 'BANK_TRANSFER', '****************', '中国银行北京分行', '采购款支付', 'system'),
('PAY202401002', 2, 30000.00, 'BANK_TRANSFER', '****************', '工商银行上海分行', '服务费支付', 'system');

-- 插入支付审批记录
INSERT INTO `payment_approval` (`payment_order_id`, `approver_id`, `approval_level`, `approval_result`, `approval_comment`, `approval_time`) VALUES
(1, 2, 1, 1, '审批通过', '2024-01-20 10:30:00'),
(2, 2, 1, 1, '审批通过', '2024-01-20 11:00:00');

-- 会计服务测试数据
USE `accounting_db`;

-- 插入会计凭证
INSERT INTO `accounting_voucher` (`voucher_no`, `settlement_batch_id`, `accounting_date`, `total_debit`, `total_credit`, `status`, `create_by`) VALUES
('VOU202401001', 1, '2024-01-20', 50000.00, 50000.00, 2, 'system'),
('VOU202401002', 2, '2024-01-20', 30000.00, 30000.00, 2, 'system');
