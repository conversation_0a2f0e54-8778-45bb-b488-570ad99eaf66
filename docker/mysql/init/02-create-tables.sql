-- 使用认证服务数据库
USE `auth_db`;

-- 系统用户表
CREATE TABLE `sys_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_code` varchar(50) NOT NULL COMMENT '用户编码',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码(加密)',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_code` (`user_code`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表';

-- 系统角色表
CREATE TABLE `sys_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_code` varchar(50) NOT NULL COMMENT '角色编码',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `description` varchar(200) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统角色表';

-- 用户角色关联表
CREATE TABLE `sys_user_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 使用单据处理服务数据库
USE `document_db`;

-- 业务单据表
CREATE TABLE `business_document` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `document_no` varchar(50) NOT NULL COMMENT '单据编号',
  `document_type` varchar(20) NOT NULL COMMENT '单据类型(PURCHASE:采购,SERVICE:服务,OTHER:其他)',
  `supplier_id` bigint NOT NULL COMMENT '供应商ID',
  `organization_id` bigint NOT NULL COMMENT '机构ID',
  `amount` decimal(15,2) NOT NULL COMMENT '单据金额',
  `currency` varchar(10) NOT NULL DEFAULT 'CNY' COMMENT '币种',
  `business_date` date NOT NULL COMMENT '业务日期',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:待处理,2:已处理,3:已结算,4:已锁定)',
  `source_system` varchar(50) DEFAULT NULL COMMENT '来源系统',
  `remark` text COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_document_no` (`document_no`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_organization_id` (`organization_id`),
  KEY `idx_business_date` (`business_date`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_composite_query` (`supplier_id`, `status`, `business_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务单据表';

-- 单据明细表
CREATE TABLE `document_detail` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `document_id` bigint NOT NULL COMMENT '单据ID',
  `item_code` varchar(50) NOT NULL COMMENT '项目编码',
  `item_name` varchar(100) NOT NULL COMMENT '项目名称',
  `quantity` decimal(10,2) NOT NULL COMMENT '数量',
  `unit_price` decimal(15,2) NOT NULL COMMENT '单价',
  `amount` decimal(15,2) NOT NULL COMMENT '金额',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `remark` text COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_document_id` (`document_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='单据明细表';

-- 使用结算服务数据库
USE `settlement_db`;

-- 供应商信息表
CREATE TABLE `supplier_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `supplier_code` varchar(50) NOT NULL COMMENT '供应商编码',
  `supplier_name` varchar(100) NOT NULL COMMENT '供应商名称',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
  `bank_name` varchar(100) DEFAULT NULL COMMENT '开户银行',
  `settlement_cycle` tinyint NOT NULL DEFAULT '30' COMMENT '结算周期(天)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_supplier_code` (`supplier_code`),
  KEY `idx_supplier_name` (`supplier_name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商信息表';

-- 结算批次表
CREATE TABLE `settlement_batch` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_no` varchar(50) NOT NULL COMMENT '结算批次号',
  `supplier_id` bigint NOT NULL COMMENT '供应商ID',
  `settlement_start_date` date NOT NULL COMMENT '结算开始日期',
  `settlement_end_date` date NOT NULL COMMENT '结算结束日期',
  `total_amount` decimal(15,2) NOT NULL COMMENT '结算总金额',
  `document_count` int NOT NULL DEFAULT '0' COMMENT '单据数量',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:待确认,2:已确认,3:已生成付款单,4:已完成)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_batch_no` (`batch_no`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_settlement_date` (`settlement_start_date`, `settlement_end_date`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='结算批次表';

-- 使用支付服务数据库
USE `payment_db`;

-- 支付订单表
CREATE TABLE `payment_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(50) NOT NULL COMMENT '支付订单号',
  `supplier_id` bigint NOT NULL COMMENT '供应商ID',
  `settlement_batch_id` bigint DEFAULT NULL COMMENT '结算批次ID',
  `payment_amount` decimal(15,2) NOT NULL COMMENT '支付金额',
  `currency` varchar(10) NOT NULL DEFAULT 'CNY' COMMENT '币种',
  `payment_method` varchar(20) NOT NULL COMMENT '支付方式(BANK_TRANSFER:银行转账)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:待审批,2:审批中,3:审批通过,4:审批拒绝,5:支付中,6:支付成功,7:支付失败)',
  `bank_account` varchar(50) DEFAULT NULL COMMENT '收款银行账号',
  `bank_name` varchar(100) DEFAULT NULL COMMENT '收款银行名称',
  `remark` text COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_settlement_batch_id` (`settlement_batch_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付订单表';

-- 支付审批表
CREATE TABLE `payment_approval` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `payment_order_id` bigint NOT NULL COMMENT '支付订单ID',
  `approver_id` bigint NOT NULL COMMENT '审批人ID',
  `approval_level` tinyint NOT NULL COMMENT '审批级别(1:一级,2:二级,3:三级)',
  `approval_result` tinyint DEFAULT NULL COMMENT '审批结果(1:通过,2:拒绝)',
  `approval_comment` varchar(500) DEFAULT NULL COMMENT '审批意见',
  `approval_time` datetime DEFAULT NULL COMMENT '审批时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_payment_order_id` (`payment_order_id`),
  KEY `idx_approver_id` (`approver_id`),
  KEY `idx_approval_level` (`approval_level`),
  KEY `idx_approval_time` (`approval_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付审批表';

-- 使用会计服务数据库
USE `accounting_db`;

-- 会计凭证表
CREATE TABLE `accounting_voucher` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `voucher_no` varchar(50) NOT NULL COMMENT '凭证号',
  `settlement_batch_id` bigint NOT NULL COMMENT '结算批次ID',
  `voucher_type` varchar(20) NOT NULL DEFAULT 'SETTLEMENT' COMMENT '凭证类型',
  `accounting_date` date NOT NULL COMMENT '会计日期',
  `total_debit` decimal(15,2) NOT NULL COMMENT '借方合计',
  `total_credit` decimal(15,2) NOT NULL COMMENT '贷方合计',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:草稿,2:已生成,3:已同步ERP,4:同步失败)',
  `erp_voucher_no` varchar(50) DEFAULT NULL COMMENT 'ERP凭证号',
  `erp_sync_time` datetime DEFAULT NULL COMMENT 'ERP同步时间',
  `sync_error_msg` text COMMENT '同步错误信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_voucher_no` (`voucher_no`),
  KEY `idx_settlement_batch_id` (`settlement_batch_id`),
  KEY `idx_accounting_date` (`accounting_date`),
  KEY `idx_status` (`status`),
  KEY `idx_erp_sync_time` (`erp_sync_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会计凭证表';
