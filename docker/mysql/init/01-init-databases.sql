-- 创建Nacos配置数据库
CREATE DATABASE IF NOT EXISTS `nacos_config` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建各个微服务数据库
CREATE DATABASE IF NOT EXISTS `auth_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `document_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `settlement_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `payment_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE IF NOT EXISTS `accounting_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户并授权
CREATE USER IF NOT EXISTS 'finance_user'@'%' IDENTIFIED BY 'finance_pass';
GRANT ALL PRIVILEGES ON `auth_db`.* TO 'finance_user'@'%';
GRANT ALL PRIVILEGES ON `document_db`.* TO 'finance_user'@'%';
GRANT ALL PRIVILEGES ON `settlement_db`.* TO 'finance_user'@'%';
GRANT ALL PRIVILEGES ON `payment_db`.* TO 'finance_user'@'%';
GRANT ALL PRIVILEGES ON `accounting_db`.* TO 'finance_user'@'%';
GRANT ALL PRIVILEGES ON `nacos_config`.* TO 'finance_user'@'%';

FLUSH PRIVILEGES;
