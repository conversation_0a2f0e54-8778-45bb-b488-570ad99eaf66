import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Row, Col, Statistic, Typography } from 'antd';
import { 
  LogoutOutlined, 
  FileTextOutlined, 
  BankOutlined, 
  DollarOutlined,
  UserOutlined 
} from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <Title level={3} className="dashboard-title">
          综合业务和财务中间件系统
        </Title>
        <div className="user-info">
          <Text style={{ color: 'white' }}>
            <UserOutlined /> 欢迎，{user?.realName || user?.username}
          </Text>
          <Text style={{ color: 'rgba(255, 255, 255, 0.7)' }}>
            角色: {user?.roles?.join(', ')}
          </Text>
          <Button 
            type="primary" 
            icon={<LogoutOutlined />} 
            onClick={handleLogout}
          >
            退出登录
          </Button>
        </div>
      </div>

      <div className="dashboard-content">
        <div className="stats-grid">
          <Card className="stat-card">
            <Statistic
              title={<span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>待处理单据</span>}
              value={23}
              prefix={<FileTextOutlined style={{ color: 'white' }} />}
              valueStyle={{ color: 'white' }}
            />
          </Card>
          
          <Card className="stat-card">
            <Statistic
              title={<span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>待结算金额</span>}
              value={1250000}
              precision={2}
              prefix={<DollarOutlined style={{ color: 'white' }} />}
              suffix="元"
              valueStyle={{ color: 'white' }}
            />
          </Card>
          
          <Card className="stat-card">
            <Statistic
              title={<span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>待审批支付</span>}
              value={8}
              prefix={<BankOutlined style={{ color: 'white' }} />}
              valueStyle={{ color: 'white' }}
            />
          </Card>
          
          <Card className="stat-card">
            <Statistic
              title={<span style={{ color: 'rgba(255, 255, 255, 0.8)' }}>活跃供应商</span>}
              value={156}
              prefix={<UserOutlined style={{ color: 'white' }} />}
              valueStyle={{ color: 'white' }}
            />
          </Card>
        </div>

        <Row gutter={[24, 24]}>
          <Col xs={24} lg={12}>
            <Card className="glass-card" title={<span style={{ color: 'white' }}>快速操作</span>}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                <Button type="primary" size="large" block>
                  创建新单据
                </Button>
                <Button type="primary" size="large" block>
                  供应商结算
                </Button>
                <Button type="primary" size="large" block>
                  支付审批
                </Button>
                <Button type="primary" size="large" block>
                  生成会计凭证
                </Button>
              </div>
            </Card>
          </Col>
          
          <Col xs={24} lg={12}>
            <Card className="glass-card" title={<span style={{ color: 'white' }}>系统状态</span>}>
              <div style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                <p>✅ 认证服务: 正常运行</p>
                <p>⏳ 单据处理服务: 开发中</p>
                <p>⏳ 结算服务: 开发中</p>
                <p>⏳ 支付服务: 开发中</p>
                <p>⏳ 会计服务: 开发中</p>
                <p style={{ marginTop: '16px', fontSize: '12px' }}>
                  当前版本: v1.0.0-MVP
                </p>
              </div>
            </Card>
          </Col>
        </Row>

        <Row style={{ marginTop: '24px' }}>
          <Col span={24}>
            <Card className="glass-card" title={<span style={{ color: 'white' }}>API测试</span>}>
              <div style={{ color: 'rgba(255, 255, 255, 0.8)' }}>
                <p>🔗 认证服务API: <a href="http://localhost:8081/swagger-ui/index.html" target="_blank" rel="noopener noreferrer" style={{ color: '#87CEEB' }}>http://localhost:8081/swagger-ui/index.html</a></p>
                <p>🔗 健康检查: <a href="http://localhost:8081/actuator/health" target="_blank" rel="noopener noreferrer" style={{ color: '#87CEEB' }}>http://localhost:8081/actuator/health</a></p>
                <p>🔗 Nacos控制台: <a href="http://localhost:8848/nacos" target="_blank" rel="noopener noreferrer" style={{ color: '#87CEEB' }}>http://localhost:8848/nacos</a> (nacos/nacos)</p>
                <p style={{ marginTop: '16px', fontSize: '14px' }}>
                  💡 提示: 当前只有认证服务可用，其他服务正在开发中。您可以测试登录、登出和Token验证功能。
                </p>
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default Dashboard;
