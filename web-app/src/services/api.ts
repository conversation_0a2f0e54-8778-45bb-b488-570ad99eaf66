import axios from 'axios';

// 创建axios实例
const api = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token过期或无效，清除本地存储并跳转到登录页
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 认证API
export const authApi = {
  login: (data: { username: string; password: string }) =>
    api.post('/auth/login', data),
  
  logout: () =>
    api.post('/auth/logout'),
  
  refreshToken: () =>
    api.post('/auth/refresh'),
  
  validateToken: () =>
    api.post('/auth/validate'),
};

// 单据API
export const documentApi = {
  getDocuments: (params?: any) =>
    api.get('/document/documents', { params }),
  
  createDocument: (data: any) =>
    api.post('/document/documents', data),
  
  getDocument: (id: number) =>
    api.get(`/document/documents/${id}`),
  
  updateDocument: (id: number, data: any) =>
    api.put(`/document/documents/${id}`, data),
  
  deleteDocument: (id: number) =>
    api.delete(`/document/documents/${id}`),
};

// 供应商API
export const supplierApi = {
  getSuppliers: (params?: any) =>
    api.get('/settlement/suppliers', { params }),
  
  createSupplier: (data: any) =>
    api.post('/settlement/suppliers', data),
  
  getSupplier: (id: number) =>
    api.get(`/settlement/suppliers/${id}`),
  
  updateSupplier: (id: number, data: any) =>
    api.put(`/settlement/suppliers/${id}`, data),
};

// 结算API
export const settlementApi = {
  getBatches: (params?: any) =>
    api.get('/settlement/batches', { params }),
  
  createBatch: (data: any) =>
    api.post('/settlement/batches', data),
  
  getBatch: (id: number) =>
    api.get(`/settlement/batches/${id}`),
  
  confirmBatch: (id: number) =>
    api.post(`/settlement/batches/${id}/confirm`),
};

// 支付API
export const paymentApi = {
  getOrders: (params?: any) =>
    api.get('/payment/orders', { params }),
  
  createOrder: (data: any) =>
    api.post('/payment/orders', data),
  
  getOrder: (id: number) =>
    api.get(`/payment/orders/${id}`),
  
  approveOrder: (id: number, data: any) =>
    api.post(`/payment/orders/${id}/approve`, data),
};

// 会计API
export const accountingApi = {
  getVouchers: (params?: any) =>
    api.get('/accounting/vouchers', { params }),
  
  createVoucher: (data: any) =>
    api.post('/accounting/vouchers', data),
  
  getVoucher: (id: number) =>
    api.get(`/accounting/vouchers/${id}`),
  
  syncToERP: (id: number) =>
    api.post(`/accounting/vouchers/${id}/sync-erp`),
};

export default api;
