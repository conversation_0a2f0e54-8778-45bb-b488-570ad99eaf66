package com.finance.auth.controller;

import com.finance.auth.application.dto.LoginCommand;
import com.finance.auth.application.dto.LoginResult;
import com.finance.auth.application.dto.RefreshTokenCommand;
import com.finance.auth.application.service.AuthApplicationService;
import com.finance.common.result.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 认证控制器（DDD架构）
 *
 * <AUTHOR> System
 */
@Tag(name = "认证管理", description = "基于DDD架构的用户认证相关接口")
@RestController
@RequestMapping("/api/auth")
public class AuthController {

    private final AuthApplicationService authApplicationService;

    public AuthController(AuthApplicationService authApplicationService) {
        this.authApplicationService = authApplicationService;
    }
    /**
     * 用户登录
     */
    @Operation(summary = "用户登录", description = "用户登录获取访问Token")
    @PostMapping("/login")
    public Result<LoginResult> login(@Valid @RequestBody LoginCommand command) {
        LoginResult result = authApplicationService.login(command);
        return Result.success("登录成功", result);
    }

    /**
     * 用户登出
     */
    @Operation(summary = "用户登出", description = "用户登出并清除Token")
    @PostMapping("/logout")
    public Result<String> logout(HttpServletRequest request) {
        String token = extractToken(request);
        authApplicationService.logout(token);
        return Result.success("登出成功");
    }

    /**
     * 刷新Token
     */
    @Operation(summary = "刷新Token", description = "使用刷新Token获取新的访问Token")
    @PostMapping("/refresh")
    public Result<LoginResult> refresh(@Valid @RequestBody RefreshTokenCommand command) {
        LoginResult result = authApplicationService.refreshToken(command);
        return Result.success("Token刷新成功", result);
    }

    /**
     * 验证Token
     */
    @Operation(summary = "验证Token", description = "验证Token是否有效")
    @PostMapping("/validate")
    public Result<Boolean> validate(HttpServletRequest request) {
        String token = extractToken(request);
        boolean valid = authApplicationService.validateToken(token);
        return Result.success("Token验证完成", valid);
    }
    
    /**
     * 从请求头中提取Token
     */
    private String extractToken(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
