package com.finance.auth.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.finance.common.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 系统角色实体
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_role")
public class SysRole extends BaseEntity {
    
    /**
     * 角色编码
     */
    @NotBlank(message = "角色编码不能为空")
    @TableField("role_code")
    private String roleCode;
    
    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @TableField("role_name")
    private String roleName;
    
    /**
     * 角色描述
     */
    @TableField("description")
    private String description;
    
    /**
     * 状态(0:禁用,1:启用)
     */
    @TableField("status")
    private Integer status;
}
