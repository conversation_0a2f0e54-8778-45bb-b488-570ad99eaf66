package com.finance.auth.service.impl;

import com.finance.auth.dto.LoginRequest;
import com.finance.auth.dto.LoginResponse;
import com.finance.auth.entity.SysUser;
import com.finance.auth.mapper.SysUserMapper;
import com.finance.auth.service.AuthService;
import com.finance.auth.util.JwtUtil;
import com.finance.common.exception.BusinessException;
import com.finance.common.result.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 认证服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {
    
    private final SysUserMapper sysUserMapper;
    private final PasswordEncoder passwordEncoder;
    private final JwtUtil jwtUtil;
    private final RedissonClient redissonClient;
    
    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        // 查询用户信息
        SysUser user = sysUserMapper.findByUsernameWithRoles(loginRequest.getUsername());
        if (user == null) {
            throw new BusinessException(ResultCode.AUTH_USERNAME_PASSWORD_ERROR);
        }
        
        // 检查用户状态
        if (user.getStatus() == 0) {
            throw new BusinessException(ResultCode.AUTH_ACCOUNT_DISABLED);
        }
        
        // 验证密码
        if (!passwordEncoder.matches(loginRequest.getPassword(), user.getPassword())) {
            throw new BusinessException(ResultCode.AUTH_USERNAME_PASSWORD_ERROR);
        }
        
        // 获取用户角色
        List<String> roleCodes = sysUserMapper.findRoleCodesByUserId(user.getId());
        
        // 生成Token
        String accessToken = jwtUtil.generateAccessToken(user.getId(), user.getUsername(), roleCodes);
        String refreshToken = jwtUtil.generateRefreshToken(user.getId(), user.getUsername());
        
        // 将Token存储到Redis
        String accessTokenKey = "auth:access_token:" + user.getId();
        String refreshTokenKey = "auth:refresh_token:" + user.getId();
        
        RBucket<String> accessTokenBucket = redissonClient.getBucket(accessTokenKey);
        RBucket<String> refreshTokenBucket = redissonClient.getBucket(refreshTokenKey);
        
        accessTokenBucket.set(accessToken, Duration.ofMillis(jwtUtil.getExpirationSeconds() * 1000));
        refreshTokenBucket.set(refreshToken, Duration.ofDays(7));
        
        // 更新最后登录时间
        user.setLastLoginTime(LocalDateTime.now());
        sysUserMapper.updateById(user);
        
        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setAccessToken(accessToken);
        response.setRefreshToken(refreshToken);
        response.setExpiresIn(jwtUtil.getExpirationSeconds());
        
        LoginResponse.UserInfo userInfo = new LoginResponse.UserInfo();
        userInfo.setId(user.getId());
        userInfo.setUsername(user.getUsername());
        userInfo.setRealName(user.getRealName());
        userInfo.setEmail(user.getEmail());
        userInfo.setRoles(roleCodes);
        response.setUserInfo(userInfo);
        
        log.info("用户登录成功: {}", user.getUsername());
        return response;
    }
    
    @Override
    public void logout(String token) {
        if (token == null || !jwtUtil.validateToken(token)) {
            return;
        }
        
        Long userId = jwtUtil.getUserIdFromToken(token);
        if (userId != null) {
            // 从Redis中删除Token
            String accessTokenKey = "auth:access_token:" + userId;
            String refreshTokenKey = "auth:refresh_token:" + userId;
            
            redissonClient.getBucket(accessTokenKey).delete();
            redissonClient.getBucket(refreshTokenKey).delete();
            
            log.info("用户登出成功: userId={}", userId);
        }
    }
    
    @Override
    public LoginResponse refreshToken(String refreshToken) {
        if (refreshToken == null || !jwtUtil.validateToken(refreshToken)) {
            throw new BusinessException(ResultCode.AUTH_TOKEN_INVALID);
        }
        
        // 检查Token类型
        if (!"refresh".equals(jwtUtil.getTokenType(refreshToken))) {
            throw new BusinessException(ResultCode.AUTH_TOKEN_INVALID);
        }
        
        Long userId = jwtUtil.getUserIdFromToken(refreshToken);
        String username = jwtUtil.getUsernameFromToken(refreshToken);
        
        if (userId == null || username == null) {
            throw new BusinessException(ResultCode.AUTH_TOKEN_INVALID);
        }
        
        // 验证Redis中的Token
        String refreshTokenKey = "auth:refresh_token:" + userId;
        RBucket<String> refreshTokenBucket = redissonClient.getBucket(refreshTokenKey);
        String storedRefreshToken = refreshTokenBucket.get();
        
        if (!refreshToken.equals(storedRefreshToken)) {
            throw new BusinessException(ResultCode.AUTH_TOKEN_INVALID);
        }
        
        // 获取用户角色
        List<String> roleCodes = sysUserMapper.findRoleCodesByUserId(userId);
        
        // 生成新的访问Token
        String newAccessToken = jwtUtil.generateAccessToken(userId, username, roleCodes);
        
        // 更新Redis中的访问Token
        String accessTokenKey = "auth:access_token:" + userId;
        RBucket<String> accessTokenBucket = redissonClient.getBucket(accessTokenKey);
        accessTokenBucket.set(newAccessToken, Duration.ofMillis(jwtUtil.getExpirationSeconds() * 1000));
        
        // 构建响应
        LoginResponse response = new LoginResponse();
        response.setAccessToken(newAccessToken);
        response.setExpiresIn(jwtUtil.getExpirationSeconds());
        
        log.info("Token刷新成功: userId={}", userId);
        return response;
    }
    
    @Override
    public boolean validateToken(String token) {
        if (token == null || !jwtUtil.validateToken(token)) {
            return false;
        }
        
        Long userId = jwtUtil.getUserIdFromToken(token);
        if (userId == null) {
            return false;
        }
        
        // 验证Redis中的Token
        String accessTokenKey = "auth:access_token:" + userId;
        RBucket<String> accessTokenBucket = redissonClient.getBucket(accessTokenKey);
        String storedToken = accessTokenBucket.get();
        
        return token.equals(storedToken);
    }
}
