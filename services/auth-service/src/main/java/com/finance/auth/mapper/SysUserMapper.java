package com.finance.auth.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.finance.auth.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 系统用户Mapper
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {
    
    /**
     * 根据用户名查询用户及其角色信息
     */
    @Select("SELECT u.*, r.role_code " +
            "FROM sys_user u " +
            "LEFT JOIN sys_user_role ur ON u.id = ur.user_id " +
            "LEFT JOIN sys_role r ON ur.role_id = r.id " +
            "WHERE u.username = #{username} AND u.status = 1")
    SysUser findByUsernameWithRoles(@Param("username") String username);
    
    /**
     * 根据用户ID查询角色编码列表
     */
    @Select("SELECT r.role_code " +
            "FROM sys_role r " +
            "INNER JOIN sys_user_role ur ON r.id = ur.role_id " +
            "WHERE ur.user_id = #{userId} AND r.status = 1")
    List<String> findRoleCodesByUserId(@Param("userId") Long userId);
}
