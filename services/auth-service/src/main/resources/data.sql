-- 创建用户表
CREATE TABLE IF NOT EXISTS sys_user (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_code VARCHAR(50) NOT NULL,
    username VARCHAR(50) NOT NULL,
    password VARCHAR(100) NOT NULL,
    real_name VARCHAR(50) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(20),
    status INT DEFAULT 1,
    last_login_time DATETIME,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(50),
    update_by VARCHAR(50)
);

-- 创建用户表的唯一索引（H2数据库兼容）
CREATE UNIQUE INDEX idx_user_code ON sys_user(user_code);
CREATE UNIQUE INDEX idx_username ON sys_user(username);

-- 创建角色表
CREATE TABLE IF NOT EXISTS sys_role (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    role_code VARCHAR(50) NOT NULL,
    role_name VARCHAR(50) NOT NULL,
    description VARCHAR(200),
    status INT DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(50),
    update_by VARCHAR(50)
);

-- 创建角色表的唯一索引
CREATE UNIQUE INDEX idx_role_code ON sys_role(role_code);

-- 创建用户角色关联表
CREATE TABLE IF NOT EXISTS sys_user_role (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(50)
);

-- 创建用户角色关联表的唯一索引
CREATE UNIQUE INDEX uk_user_role ON sys_user_role(user_id, role_id);

-- 插入测试角色数据
INSERT INTO sys_role (role_code, role_name, description, create_by) VALUES
('ADMIN', '系统管理员', '系统管理员角色，拥有所有权限', 'system'),
('USER', '普通用户', '普通用户角色，拥有基本权限', 'system'),
('FINANCE', '财务人员', '财务人员角色，拥有财务相关权限', 'system');

-- 插入测试用户数据（密码都是123456，已加密）
INSERT INTO sys_user (user_code, username, password, real_name, email, phone, create_by) VALUES
('U001', 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKi85KOKdYNQJlOOJUgKOKWCi', '系统管理员', '<EMAIL>', '13800138001', 'system'),
('U002', 'user', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKi85KOKdYNQJlOOJUgKOKWCi', '普通用户', '<EMAIL>', '13800138002', 'system'),
('U003', 'finance', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8ioctKi85KOKdYNQJlOOJUgKOKWCi', '财务人员', '<EMAIL>', '13800138003', 'system');

-- 插入用户角色关联数据
INSERT INTO sys_user_role (user_id, role_id, create_by) VALUES
(1, 1, 'system'), -- admin用户拥有管理员角色
(1, 2, 'system'), -- admin用户拥有普通用户角色
(2, 2, 'system'), -- user用户拥有普通用户角色
(3, 2, 'system'), -- finance用户拥有普通用户角色
(3, 3, 'system'); -- finance用户拥有财务人员角色
