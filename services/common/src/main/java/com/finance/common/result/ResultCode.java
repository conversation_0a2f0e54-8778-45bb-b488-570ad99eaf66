package com.finance.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 */
@Getter
@AllArgsConstructor
public enum ResultCode {
    
    // 成功
    SUCCESS(200, "操作成功"),
    
    // 客户端错误
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未认证或认证失败"),
    FORBIDDEN(403, "无权限访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    UNPROCESSABLE_ENTITY(422, "请求格式正确但语义错误"),
    
    // 服务器错误
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),
    
    // 业务错误码 (2000-2999)
    BUSINESS_ERROR(2000, "业务处理失败"),
    
    // 认证授权错误码 (1000-1099)
    AUTH_USERNAME_PASSWORD_ERROR(1001, "用户名或密码错误"),
    AUTH_ACCOUNT_DISABLED(1002, "账号已被禁用"),
    AUTH_TOKEN_EXPIRED(1003, "Token已过期"),
    AUTH_TOKEN_INVALID(1004, "Token无效"),
    AUTH_PERMISSION_DENIED(1005, "权限不足"),
    
    // 业务错误码 (2000-2999)
    BIZ_SUPPLIER_NOT_FOUND(2001, "供应商不存在"),
    BIZ_DOCUMENT_NOT_FOUND(2002, "单据不存在"),
    BIZ_DOCUMENT_STATUS_ERROR(2003, "单据状态不允许操作"),
    BIZ_SETTLEMENT_BATCH_NOT_FOUND(2004, "结算批次不存在"),
    BIZ_PAYMENT_ORDER_NOT_FOUND(2005, "支付订单不存在"),
    BIZ_APPROVAL_WORKFLOW_ERROR(2006, "审批流程异常"),
    BIZ_BANK_PAYMENT_FAILED(2007, "银行支付失败"),
    BIZ_ERP_SYNC_FAILED(2008, "ERP同步失败"),
    
    // 系统错误码 (9000-9999)
    SYS_INTERNAL_ERROR(9001, "系统内部错误"),
    SYS_SERVICE_UNAVAILABLE(9002, "服务不可用"),
    SYS_REQUEST_TIMEOUT(9003, "请求超时"),
    SYS_PARAMETER_VALIDATION_FAILED(9004, "参数验证失败"),
    SYS_DATABASE_CONNECTION_FAILED(9005, "数据库连接失败");
    
    private final Integer code;
    private final String message;
}
