# 综合业务和财务中间件系统

基于Spring Cloud Alibaba微服务架构的企业级财务管理系统，采用现代化技术栈和glassmorphism UI设计。

## 🏗️ 系统架构

### 技术栈
- **后端**: Spring Boot 3.2 + Spring Cloud Alibaba
- **数据库**: MySQL 8.0 + MyBatis-Plus
- **缓存**: Redis 7.0 + Redisson
- **消息队列**: Apache Pulsar 3.1
- **服务发现**: Nacos 2.2
- **前端**: React 18 + TypeScript + Ant Design Pro
- **容器化**: Docker + Docker Compose

### 微服务架构
- **认证服务** (auth-service): JWT认证、用户管理、权限控制
- **单据处理服务** (document-service): 业务单据管理
- **供应商结算服务** (settlement-service): 供应商结算处理
- **支付服务** (payment-service): 支付申请、审批、执行
- **财务会计服务** (accounting-service): 会计凭证生成、ERP同步
- **API网关** (gateway-service): 统一入口、路由、负载均衡

## 🚀 快速开始

### 环境要求
- Docker 20.10+
- Docker Compose 2.0+
- Maven 3.8+ (可选，用于本地开发)
- Node.js 18+ (可选，用于前端开发)

### 一键启动
```bash
# 克隆项目
git clone <repository-url>
cd integratedbusinessandfinancesystem

# 启动开发环境
./start-dev.sh
```

### 手动启动步骤

1. **启动基础设施服务**
```bash
docker-compose up -d mysql redis nacos pulsar
```

2. **等待服务就绪** (约30秒)

3. **构建并启动应用服务**
```bash
# 构建Maven项目
mvn clean package -DskipTests

# 启动认证服务
docker-compose up -d auth-service

# 启动前端应用 (可选)
cd web-app
npm install
npm start
```

## 📱 访问地址

### 主要服务
- **前端应用**: http://localhost:3000
- **认证服务**: http://localhost:8081
- **API网关**: http://localhost:8080

### 管理控制台
- **Nacos控制台**: http://localhost:8848/nacos
  - 用户名/密码: `nacos/nacos`
- **认证服务API文档**: http://localhost:8081/swagger-ui/index.html
- **数据库监控**: http://localhost:8081/druid
  - 用户名/密码: `admin/admin123`

### 数据库连接
- **MySQL**: localhost:3306
  - 用户名/密码: `finance_user/finance_pass`
- **Redis**: localhost:6379

## 🔐 测试账号

| 角色 | 用户名 | 密码 | 权限 |
|------|--------|------|------|
| 系统管理员 | admin | 123456 | 所有权限 |
| 财务经理 | finance_manager | 123456 | 财务管理和审批 |
| 财务专员 | finance_staff | 123456 | 日常财务操作 |
| 供应商用户 | supplier_user | 123456 | 查看自身信息 |

## 🧪 功能测试

### 自动化测试
```bash
# 运行系统功能测试
./test-system.sh
```

### 手动测试API

1. **用户登录**
```bash
curl -X POST http://localhost:8081/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}'
```

2. **Token验证**
```bash
curl -X POST http://localhost:8081/auth/validate \
  -H "Authorization: Bearer YOUR_TOKEN"
```

3. **用户登出**
```bash
curl -X POST http://localhost:8081/auth/logout \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 📊 系统监控

### 健康检查
- 认证服务: http://localhost:8081/actuator/health
- 数据库状态: 通过Druid监控页面查看
- Redis状态: `docker-compose exec redis redis-cli ping`

### 日志查看
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f auth-service
docker-compose logs -f mysql
```

## 🛠️ 开发指南

### 本地开发环境

1. **启动基础设施**
```bash
docker-compose up -d mysql redis nacos pulsar
```

2. **本地运行服务**
```bash
cd services/auth-service
mvn spring-boot:run
```

3. **前端开发**
```bash
cd web-app
npm install
npm start
```

### 代码结构
```
├── docs/                    # 技术规范文档
├── services/                # 微服务代码
│   ├── common/             # 公共模块
│   ├── auth-service/       # 认证服务
│   ├── document-service/   # 单据处理服务
│   ├── settlement-service/ # 结算服务
│   ├── payment-service/    # 支付服务
│   └── accounting-service/ # 会计服务
├── web-app/                # 前端应用
├── docker/                 # Docker配置
└── docker-compose.yml      # 容器编排
```

## 🔧 配置说明

### 环境变量
- `SPRING_PROFILES_ACTIVE`: 运行环境 (dev/test/prod)
- `NACOS_SERVER_ADDR`: Nacos服务地址
- `MYSQL_HOST`: MySQL主机地址
- `REDIS_HOST`: Redis主机地址

### 数据库配置
系统自动创建以下数据库：
- `auth_db`: 认证服务数据库
- `document_db`: 单据处理数据库
- `settlement_db`: 结算服务数据库
- `payment_db`: 支付服务数据库
- `accounting_db`: 会计服务数据库

## 🚨 故障排除

### 常见问题

1. **服务启动失败**
   - 检查Docker是否运行
   - 确认端口未被占用
   - 查看服务日志: `docker-compose logs service-name`

2. **数据库连接失败**
   - 等待MySQL完全启动 (约30秒)
   - 检查数据库用户权限
   - 验证网络连接

3. **前端无法访问后端**
   - 确认API网关正常运行
   - 检查CORS配置
   - 验证Token是否有效

### 重置系统
```bash
# 停止所有服务并清理数据
docker-compose down -v

# 清理Docker资源
docker system prune -f

# 重新启动
./start-dev.sh
```

## 📈 性能优化

### 生产环境建议
- 使用专用的MySQL和Redis集群
- 配置Nginx反向代理
- 启用JVM性能调优参数
- 配置监控和告警系统

### 扩展性
- 支持水平扩展微服务实例
- 数据库读写分离
- Redis集群模式
- 消息队列集群部署

## 📝 更新日志

### v1.0.0-MVP (当前版本)
- ✅ 认证服务完整实现
- ✅ 基础设施搭建完成
- ✅ 前端登录界面
- ✅ Docker容器化部署
- ⏳ 其他微服务开发中

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码变更
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 项目Issues: [GitHub Issues](https://github.com/your-repo/issues)
- 邮箱: <EMAIL>

---

**注意**: 当前版本为MVP版本，仅实现了认证服务。其他微服务正在开发中，预计在后续版本中逐步发布。
