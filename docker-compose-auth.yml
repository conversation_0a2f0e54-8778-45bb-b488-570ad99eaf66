services:
  # 认证服务
  auth-service:
    build:
      context: ./services/auth-service
      dockerfile: Dockerfile
    container_name: finance-auth
    ports:
      - "8081:8081"
    environment:
      SPRING_PROFILES_ACTIVE: docker
    networks:
      - finance-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8081/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  finance-network:
    external: true
