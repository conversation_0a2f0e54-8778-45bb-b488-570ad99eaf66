#!/bin/bash

echo "=== 综合业务和财务中间件系统 - 功能测试脚本 ==="

# 基础URL
AUTH_URL="http://localhost:8081"
API_BASE_URL="http://localhost:8080"

echo "1. 测试基础设施服务..."

# 测试MySQL
echo "测试MySQL连接..."
docker-compose exec mysql mysqladmin ping -h localhost -u finance_user -pfinance_pass 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✅ MySQL连接正常"
else
    echo "❌ MySQL连接失败"
fi

# 测试Redis
echo "测试Redis连接..."
REDIS_RESULT=$(docker-compose exec redis redis-cli ping 2>/dev/null)
if [ "$REDIS_RESULT" = "PONG" ]; then
    echo "✅ Redis连接正常"
else
    echo "❌ Redis连接失败"
fi

# 测试Nacos
echo "测试Nacos服务..."
NACOS_RESULT=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8848/nacos/actuator/health)
if [ "$NACOS_RESULT" = "200" ]; then
    echo "✅ Nacos服务正常"
else
    echo "❌ Nacos服务异常"
fi

echo ""
echo "2. 测试认证服务..."

# 测试认证服务健康检查
echo "测试认证服务健康检查..."
AUTH_HEALTH=$(curl -s -o /dev/null -w "%{http_code}" $AUTH_URL/actuator/health)
if [ "$AUTH_HEALTH" = "200" ]; then
    echo "✅ 认证服务健康检查正常"
else
    echo "❌ 认证服务健康检查失败"
    exit 1
fi

# 测试用户登录
echo "测试用户登录..."
LOGIN_RESPONSE=$(curl -s -X POST $AUTH_URL/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}')

echo "登录响应: $LOGIN_RESPONSE"

# 提取Token
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"accessToken":"[^"]*"' | cut -d'"' -f4)

if [ -n "$TOKEN" ]; then
    echo "✅ 用户登录成功，Token: ${TOKEN:0:20}..."
else
    echo "❌ 用户登录失败"
    exit 1
fi

# 测试Token验证
echo "测试Token验证..."
VALIDATE_RESPONSE=$(curl -s -X POST $AUTH_URL/auth/validate \
  -H "Authorization: Bearer $TOKEN")

echo "Token验证响应: $VALIDATE_RESPONSE"

if echo $VALIDATE_RESPONSE | grep -q '"data":true'; then
    echo "✅ Token验证成功"
else
    echo "❌ Token验证失败"
fi

# 测试用户登出
echo "测试用户登出..."
LOGOUT_RESPONSE=$(curl -s -X POST $AUTH_URL/auth/logout \
  -H "Authorization: Bearer $TOKEN")

echo "登出响应: $LOGOUT_RESPONSE"

if echo $LOGOUT_RESPONSE | grep -q '"code":200'; then
    echo "✅ 用户登出成功"
else
    echo "❌ 用户登出失败"
fi

echo ""
echo "3. 测试数据库数据..."

# 检查测试数据
echo "检查用户数据..."
USER_COUNT=$(docker-compose exec mysql mysql -u finance_user -pfinance_pass -D auth_db -e "SELECT COUNT(*) FROM sys_user;" -s -N 2>/dev/null)
echo "用户数量: $USER_COUNT"

echo "检查供应商数据..."
SUPPLIER_COUNT=$(docker-compose exec mysql mysql -u finance_user -pfinance_pass -D settlement_db -e "SELECT COUNT(*) FROM supplier_info;" -s -N 2>/dev/null)
echo "供应商数量: $SUPPLIER_COUNT"

echo "检查单据数据..."
DOCUMENT_COUNT=$(docker-compose exec mysql mysql -u finance_user -pfinance_pass -D document_db -e "SELECT COUNT(*) FROM business_document;" -s -N 2>/dev/null)
echo "单据数量: $DOCUMENT_COUNT"

echo ""
echo "4. 性能测试..."

# 简单的并发登录测试
echo "执行并发登录测试 (10个并发请求)..."
for i in {1..10}; do
    curl -s -X POST $AUTH_URL/auth/login \
      -H "Content-Type: application/json" \
      -d '{"username":"admin","password":"123456"}' > /dev/null &
done
wait
echo "✅ 并发登录测试完成"

echo ""
echo "=== 测试完成 ==="
echo ""
echo "📊 测试结果总结:"
echo "- 基础设施服务: MySQL, Redis, Nacos"
echo "- 认证服务: 登录, Token验证, 登出"
echo "- 数据库: 测试数据已就绪"
echo "- 性能: 基础并发测试通过"
echo ""
echo "🌐 访问地址:"
echo "- 前端应用: http://localhost:3000"
echo "- 认证服务API文档: http://localhost:8081/swagger-ui/index.html"
echo "- Nacos控制台: http://localhost:8848/nacos (nacos/nacos)"
echo ""
echo "🔑 测试账号:"
echo "- 管理员: admin / 123456"
echo "- 财务经理: finance_manager / 123456"
echo "- 财务专员: finance_staff / 123456"
