# API规范文档 - 综合业务和财务中间件系统

## 1. RESTful API设计规范

### 1.1 API设计原则

#### 1.1.1 统一资源标识符（URI）设计
- **基础URL格式**: `https://api.domain.com/v1/{service}/{resource}`
- **服务前缀**: 
  - `/auth` - 认证服务
  - `/document` - 单据处理服务
  - `/settlement` - 供应商结算服务
  - `/payment` - 支付服务
  - `/accounting` - 财务会计服务
  - `/config` - 配置管理服务

#### 1.1.2 HTTP方法使用规范
- **GET**: 查询资源，幂等操作
- **POST**: 创建资源，非幂等操作
- **PUT**: 完整更新资源，幂等操作
- **PATCH**: 部分更新资源，幂等操作
- **DELETE**: 删除资源，幂等操作

#### 1.1.3 命名规范
- **资源名称**: 使用复数形式，如 `/users`, `/documents`, `/payments`
- **路径参数**: 使用小写字母和连字符，如 `/supplier-info`
- **查询参数**: 使用驼峰命名，如 `?pageSize=10&sortBy=createTime`

### 1.2 HTTP状态码使用标准

#### 1.2.1 成功状态码
- **200 OK**: 请求成功，返回数据
- **201 Created**: 资源创建成功
- **204 No Content**: 请求成功，无返回内容（如删除操作）

#### 1.2.2 客户端错误状态码
- **400 Bad Request**: 请求参数错误
- **401 Unauthorized**: 未认证或认证失败
- **403 Forbidden**: 无权限访问
- **404 Not Found**: 资源不存在
- **409 Conflict**: 资源冲突（如重复创建）
- **422 Unprocessable Entity**: 请求格式正确但语义错误

#### 1.2.3 服务器错误状态码
- **500 Internal Server Error**: 服务器内部错误
- **502 Bad Gateway**: 网关错误
- **503 Service Unavailable**: 服务不可用

### 1.3 统一响应格式

#### 1.3.1 成功响应格式
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体业务数据
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```

#### 1.3.2 分页响应格式
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      // 数据列表
    ],
    "pagination": {
      "current": 1,
      "size": 10,
      "total": 100,
      "pages": 10
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```

#### 1.3.3 错误响应格式
```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": {
    "errorCode": "INVALID_PARAMETER",
    "errorMessage": "供应商ID不能为空",
    "details": [
      {
        "field": "supplierId",
        "message": "不能为空"
      }
    ]
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "traceId": "abc123def456"
}
```

### 1.4 API版本控制策略

#### 1.4.1 版本控制方式
- **URL路径版本控制**: `/v1/`, `/v2/` (推荐)
- **请求头版本控制**: `API-Version: v1`

#### 1.4.2 版本兼容性
- **向后兼容**: 新版本保持对旧版本的兼容
- **废弃通知**: 提前6个月通知API废弃
- **版本生命周期**: 每个版本至少维护2年

## 2. 核心业务API接口定义

### 2.1 用户认证和授权API

#### 2.1.1 用户登录
```yaml
POST /v1/auth/login
Content-Type: application/json

# 请求体
{
  "username": "admin",
  "password": "password123",
  "captcha": "abc123"
}

# 响应体
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 7200,
    "userInfo": {
      "id": 1,
      "username": "admin",
      "realName": "管理员",
      "email": "<EMAIL>",
      "roles": ["ADMIN", "FINANCE_MANAGER"]
    }
  }
}
```

#### 2.1.2 刷新Token
```yaml
POST /v1/auth/refresh
Authorization: Bearer {refreshToken}

# 响应体
{
  "code": 200,
  "message": "Token刷新成功",
  "data": {
    "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 7200
  }
}
```

#### 2.1.3 用户登出
```yaml
POST /v1/auth/logout
Authorization: Bearer {accessToken}

# 响应体
{
  "code": 200,
  "message": "登出成功"
}
```

### 2.2 单据处理服务API

#### 2.2.1 查询单据列表
```yaml
GET /v1/document/documents
Authorization: Bearer {accessToken}
Parameters:
  - supplierId: 供应商ID (optional)
  - status: 单据状态 (optional)
  - startDate: 开始日期 (optional)
  - endDate: 结束日期 (optional)
  - current: 当前页码 (default: 1)
  - size: 每页大小 (default: 10)

# 响应体
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "documentNo": "DOC202401150001",
        "documentType": "PURCHASE",
        "supplierName": "供应商A",
        "amount": 10000.00,
        "currency": "CNY",
        "businessDate": "2024-01-15",
        "status": 1,
        "statusName": "待处理",
        "createTime": "2024-01-15T10:30:00Z"
      }
    ],
    "pagination": {
      "current": 1,
      "size": 10,
      "total": 100,
      "pages": 10
    }
  }
}
```

#### 2.2.2 创建单据
```yaml
POST /v1/document/documents
Authorization: Bearer {accessToken}
Content-Type: application/json

# 请求体
{
  "documentNo": "DOC202401150001",
  "documentType": "PURCHASE",
  "supplierId": 1,
  "organizationId": 1,
  "amount": 10000.00,
  "currency": "CNY",
  "businessDate": "2024-01-15",
  "sourceSystem": "ERP",
  "remark": "采购单据",
  "details": [
    {
      "itemCode": "ITEM001",
      "itemName": "商品A",
      "quantity": 10,
      "unitPrice": 1000.00,
      "amount": 10000.00,
      "unit": "个"
    }
  ]
}

# 响应体
{
  "code": 201,
  "message": "单据创建成功",
  "data": {
    "id": 1,
    "documentNo": "DOC202401150001"
  }
}
```

### 2.3 供应商结算服务API

#### 2.3.1 查询结算批次列表
```yaml
GET /v1/settlement/batches
Authorization: Bearer {accessToken}
Parameters:
  - supplierId: 供应商ID (optional)
  - status: 结算状态 (optional)
  - startDate: 开始日期 (optional)
  - endDate: 结束日期 (optional)

# 响应体
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "batchNo": "BATCH202401150001",
        "supplierName": "供应商A",
        "settlementStartDate": "2024-01-01",
        "settlementEndDate": "2024-01-15",
        "totalAmount": 50000.00,
        "documentCount": 5,
        "status": 2,
        "statusName": "已确认",
        "createTime": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

#### 2.3.2 创建结算批次
```yaml
POST /v1/settlement/batches
Authorization: Bearer {accessToken}
Content-Type: application/json

# 请求体
{
  "supplierId": 1,
  "settlementStartDate": "2024-01-01",
  "settlementEndDate": "2024-01-15",
  "documentIds": [1, 2, 3, 4, 5]
}

# 响应体
{
  "code": 201,
  "message": "结算批次创建成功",
  "data": {
    "id": 1,
    "batchNo": "BATCH202401150001",
    "totalAmount": 50000.00,
    "documentCount": 5
  }
}
```

### 2.4 支付服务API

#### 2.4.1 创建支付申请
```yaml
POST /v1/payment/orders
Authorization: Bearer {accessToken}
Content-Type: application/json

# 请求体
{
  "supplierId": 1,
  "settlementBatchId": 1,
  "paymentAmount": 50000.00,
  "currency": "CNY",
  "paymentMethod": "BANK_TRANSFER",
  "bankAccount": "**********",
  "bankName": "中国银行",
  "remark": "供应商结算付款"
}

# 响应体
{
  "code": 201,
  "message": "支付申请创建成功",
  "data": {
    "id": 1,
    "orderNo": "PAY202401150001",
    "status": 1,
    "statusName": "待审批"
  }
}
```

#### 2.4.2 支付审批
```yaml
POST /v1/payment/orders/{orderId}/approve
Authorization: Bearer {accessToken}
Content-Type: application/json

# 请求体
{
  "approvalResult": 1,
  "approvalComment": "审批通过"
}

# 响应体
{
  "code": 200,
  "message": "审批成功",
  "data": {
    "orderId": 1,
    "approvalLevel": 1,
    "nextApprover": "二级审批人"
  }
}
```

### 2.5 财务会计服务API

#### 2.5.1 生成会计凭证
```yaml
POST /v1/accounting/vouchers
Authorization: Bearer {accessToken}
Content-Type: application/json

# 请求体
{
  "settlementBatchId": 1,
  "accountingDate": "2024-01-15",
  "voucherType": "SETTLEMENT"
}

# 响应体
{
  "code": 201,
  "message": "会计凭证生成成功",
  "data": {
    "id": 1,
    "voucherNo": "VOU202401150001",
    "totalDebit": 50000.00,
    "totalCredit": 50000.00,
    "status": 2,
    "statusName": "已生成"
  }
}
```

#### 2.5.2 同步凭证到ERP
```yaml
POST /v1/accounting/vouchers/{voucherId}/sync-erp
Authorization: Bearer {accessToken}

# 响应体
{
  "code": 200,
  "message": "ERP同步成功",
  "data": {
    "voucherId": 1,
    "erpVoucherNo": "ERP202401150001",
    "syncTime": "2024-01-15T10:30:00Z"
  }
}
```

## 3. 外部系统集成API

### 3.1 金蝶ERP系统集成接口

#### 3.1.1 凭证同步接口
```yaml
POST /kingdee/api/voucher/sync
Content-Type: application/json
Authorization: Bearer {erpToken}

# 请求体
{
  "voucherData": {
    "voucherNo": "VOU202401150001",
    "accountingDate": "2024-01-15",
    "voucherType": "记账凭证",
    "entries": [
      {
        "accountCode": "2202001",
        "accountName": "应付账款-供应商A",
        "debitAmount": 50000.00,
        "creditAmount": 0.00,
        "summary": "供应商结算"
      },
      {
        "accountCode": "1002001",
        "accountName": "银行存款-基本户",
        "debitAmount": 0.00,
        "creditAmount": 50000.00,
        "summary": "供应商结算"
      }
    ]
  }
}
```

### 3.2 银行支付接口集成

#### 3.2.1 银行转账接口
```yaml
POST /bank/api/transfer
Content-Type: application/json
Authorization: Bearer {bankToken}

# 请求体
{
  "paymentOrder": {
    "orderNo": "PAY202401150001",
    "payerAccount": "**********123456",
    "payeeAccount": "****************",
    "payeeName": "供应商A",
    "payeeBank": "中国银行",
    "amount": 50000.00,
    "currency": "CNY",
    "purpose": "货款支付",
    "urgency": "NORMAL"
  }
}

# 响应体
{
  "code": "0000",
  "message": "交易成功",
  "data": {
    "bankTransactionNo": "BANK202401150001",
    "status": "SUCCESS",
    "executeTime": "2024-01-15T10:30:00Z"
  }
}
```

## 4. OpenAPI 3.0 规范示例

### 4.1 支付服务OpenAPI规范
```yaml
openapi: 3.0.3
info:
  title: 支付服务API
  description: 综合业务和财务中间件系统 - 支付服务API
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: https://api.company.com/v1/payment
    description: 生产环境
  - url: https://api-test.company.com/v1/payment
    description: 测试环境

security:
  - BearerAuth: []

paths:
  /orders:
    post:
      summary: 创建支付申请
      description: 创建新的支付申请订单
      tags:
        - 支付订单
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentOrderRequest'
            example:
              supplierId: 1
              settlementBatchId: 1
              paymentAmount: 50000.00
              currency: "CNY"
              paymentMethod: "BANK_TRANSFER"
              bankAccount: "**********"
              bankName: "中国银行"
              remark: "供应商结算付款"
      responses:
        '201':
          description: 支付申请创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
              example:
                code: 201
                message: "支付申请创建成功"
                data:
                  id: 1
                  orderNo: "PAY202401150001"
                  status: 1
                  statusName: "待审批"
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未授权
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /orders/{orderId}/approve:
    post:
      summary: 支付审批
      description: 对支付订单进行审批操作
      tags:
        - 支付审批
      parameters:
        - name: orderId
          in: path
          required: true
          description: 支付订单ID
          schema:
            type: integer
            format: int64
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApprovalRequest'
      responses:
        '200':
          description: 审批成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应状态码
        message:
          type: string
          description: 响应消息
        data:
          type: object
          description: 响应数据
        timestamp:
          type: string
          format: date-time
          description: 响应时间戳
        traceId:
          type: string
          description: 链路追踪ID

    ErrorResponse:
      type: object
      properties:
        code:
          type: integer
          description: 错误状态码
        message:
          type: string
          description: 错误消息
        error:
          type: object
          properties:
            errorCode:
              type: string
              description: 错误代码
            errorMessage:
              type: string
              description: 详细错误信息
            details:
              type: array
              items:
                type: object
                properties:
                  field:
                    type: string
                  message:
                    type: string

    PaymentOrderRequest:
      type: object
      required:
        - supplierId
        - paymentAmount
        - currency
        - paymentMethod
      properties:
        supplierId:
          type: integer
          format: int64
          description: 供应商ID
        settlementBatchId:
          type: integer
          format: int64
          description: 结算批次ID
        paymentAmount:
          type: number
          format: decimal
          description: 支付金额
        currency:
          type: string
          enum: [CNY, USD, EUR]
          description: 币种
        paymentMethod:
          type: string
          enum: [BANK_TRANSFER, ONLINE_PAYMENT]
          description: 支付方式
        bankAccount:
          type: string
          description: 收款银行账号
        bankName:
          type: string
          description: 收款银行名称
        remark:
          type: string
          description: 备注

    ApprovalRequest:
      type: object
      required:
        - approvalResult
      properties:
        approvalResult:
          type: integer
          enum: [1, 2]
          description: 审批结果 (1:通过, 2:拒绝)
        approvalComment:
          type: string
          description: 审批意见
```

## 5. 数据模型定义

### 5.1 核心业务实体模型

#### 5.1.1 用户信息模型
```typescript
interface UserInfo {
  id: number;
  userCode: string;
  username: string;
  realName: string;
  email?: string;
  phone?: string;
  status: UserStatus;
  roles: string[];
  lastLoginTime?: string;
  createTime: string;
  updateTime: string;
}

enum UserStatus {
  DISABLED = 0,
  ENABLED = 1
}
```

#### 5.1.2 业务单据模型
```typescript
interface BusinessDocument {
  id: number;
  documentNo: string;
  documentType: DocumentType;
  supplierId: number;
  supplierName: string;
  organizationId: number;
  amount: number;
  currency: string;
  businessDate: string;
  status: DocumentStatus;
  statusName: string;
  sourceSystem?: string;
  remark?: string;
  details: DocumentDetail[];
  createTime: string;
  updateTime: string;
}

enum DocumentType {
  PURCHASE = 'PURCHASE',
  SERVICE = 'SERVICE',
  OTHER = 'OTHER'
}

enum DocumentStatus {
  PENDING = 1,
  PROCESSED = 2,
  SETTLED = 3,
  LOCKED = 4
}

interface DocumentDetail {
  id: number;
  itemCode: string;
  itemName: string;
  quantity: number;
  unitPrice: number;
  amount: number;
  unit: string;
  remark?: string;
}
```

#### 5.1.3 支付订单模型
```typescript
interface PaymentOrder {
  id: number;
  orderNo: string;
  supplierId: number;
  supplierName: string;
  settlementBatchId?: number;
  paymentAmount: number;
  currency: string;
  paymentMethod: PaymentMethod;
  status: PaymentStatus;
  statusName: string;
  bankAccount?: string;
  bankName?: string;
  approvals: PaymentApproval[];
  execution?: PaymentExecution;
  remark?: string;
  createTime: string;
  updateTime: string;
}

enum PaymentMethod {
  BANK_TRANSFER = 'BANK_TRANSFER',
  ONLINE_PAYMENT = 'ONLINE_PAYMENT'
}

enum PaymentStatus {
  PENDING_APPROVAL = 1,
  IN_APPROVAL = 2,
  APPROVED = 3,
  REJECTED = 4,
  PAYING = 5,
  PAID = 6,
  FAILED = 7
}

interface PaymentApproval {
  id: number;
  approverId: number;
  approverName: string;
  approvalLevel: number;
  approvalResult?: ApprovalResult;
  approvalComment?: string;
  approvalTime?: string;
  createTime: string;
}

enum ApprovalResult {
  APPROVED = 1,
  REJECTED = 2
}
```

## 6. 错误码定义

### 6.1 系统级错误码
- **SYS_0001**: 系统内部错误
- **SYS_0002**: 服务不可用
- **SYS_0003**: 请求超时
- **SYS_0004**: 参数验证失败
- **SYS_0005**: 数据库连接失败

### 6.2 认证授权错误码
- **AUTH_1001**: 用户名或密码错误
- **AUTH_1002**: 账号已被禁用
- **AUTH_1003**: Token已过期
- **AUTH_1004**: Token无效
- **AUTH_1005**: 权限不足

### 6.3 业务错误码
- **BIZ_2001**: 供应商不存在
- **BIZ_2002**: 单据不存在
- **BIZ_2003**: 单据状态不允许操作
- **BIZ_2004**: 结算批次不存在
- **BIZ_2005**: 支付订单不存在
- **BIZ_2006**: 审批流程异常
- **BIZ_2007**: 银行支付失败
- **BIZ_2008**: ERP同步失败

## 7. API安全规范

### 7.1 认证机制
- **JWT Token**: 使用JWT进行用户认证
- **Token过期时间**: 访问Token 2小时，刷新Token 7天
- **Token刷新**: 支持无感知Token刷新

### 7.2 授权机制
- **RBAC权限模型**: 基于角色的访问控制
- **接口权限**: 每个接口都需要相应的权限标识
- **数据权限**: 基于用户所属机构的数据访问控制

### 7.3 安全防护
- **请求限流**: 基于用户和IP的请求频率限制
- **参数校验**: 严格的输入参数验证
- **SQL注入防护**: 使用参数化查询
- **XSS防护**: 输出内容转义处理

## 8. API监控和日志

### 8.1 监控指标
- **响应时间**: 接口平均响应时间和P99响应时间
- **成功率**: 接口调用成功率
- **QPS**: 每秒请求数
- **错误率**: 接口错误率统计

### 8.2 日志规范
- **访问日志**: 记录所有API调用信息
- **错误日志**: 记录系统异常和业务错误
- **审计日志**: 记录关键业务操作
- **性能日志**: 记录慢查询和性能问题

---

*本文档定义了系统完整的API规范，包括RESTful设计原则、接口定义、数据模型、错误处理和安全规范，为前后端开发和系统集成提供了详细的技术标准。*
