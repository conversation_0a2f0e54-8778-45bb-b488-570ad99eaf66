# 系统架构图 - 综合业务和财务中间件系统

## 1. 系统整体架构图

### 1.1 总体架构概览
以下架构图展示了系统的整体结构，包括前端应用、API网关、微服务集群、数据存储和外部系统集成。

```mermaid
graph TB
    %% 前端层
    subgraph "前端应用层"
        WebApp[Web管理后台<br/>React + Ant Design Pro]
        MobileApp[移动端应用<br/>PWA + 响应式设计]
    end
    
    %% 网关层
    subgraph "API网关层"
        Gateway[Spring Cloud Gateway<br/>统一入口 + 负载均衡]
        Auth[认证服务<br/>JWT + OAuth2]
    end
    
    %% 微服务层
    subgraph "微服务集群"
        DocService[单据处理服务<br/>Document Processing]
        SettlementService[供应商结算服务<br/>Supplier Settlement]
        AccountingService[财务会计服务<br/>Financial Accounting]
        PaymentService[自动化支付服务<br/>Automated Payment]
        ConfigService[配置管理服务<br/>Configuration Management]
    end
    
    %% 数据存储层
    subgraph "数据存储层"
        MySQL[(MySQL 8.0<br/>主数据库集群)]
        Redis[(Redis 7.0<br/>缓存 + 分布式锁)]
        Pulsar[Apache Pulsar<br/>消息队列]
    end
    
    %% 外部系统
    subgraph "外部系统集成"
        KingdeeERP[金蝶ERP系统<br/>财务数据同步]
        BankAPI[银行支付接口<br/>自动化支付]
        ThirdPartyAPI[第三方API<br/>数据源系统]
    end
    
    %% 基础设施
    subgraph "基础设施层"
        Nacos[Nacos<br/>服务发现 + 配置中心]
        Sentinel[Sentinel<br/>流量控制 + 熔断]
        SkyWalking[SkyWalking<br/>链路追踪]
        ELK[ELK Stack<br/>日志收集分析]
    end
    
    %% 连接关系
    WebApp --> Gateway
    MobileApp --> Gateway
    Gateway --> Auth
    Gateway --> DocService
    Gateway --> SettlementService
    Gateway --> AccountingService
    Gateway --> PaymentService
    Gateway --> ConfigService
    
    DocService --> MySQL
    SettlementService --> MySQL
    AccountingService --> MySQL
    PaymentService --> MySQL
    ConfigService --> MySQL
    
    DocService --> Redis
    SettlementService --> Redis
    AccountingService --> Redis
    PaymentService --> Redis
    
    DocService --> Pulsar
    SettlementService --> Pulsar
    AccountingService --> Pulsar
    PaymentService --> Pulsar
    
    AccountingService --> KingdeeERP
    PaymentService --> BankAPI
    DocService --> ThirdPartyAPI
    
    DocService --> Nacos
    SettlementService --> Nacos
    AccountingService --> Nacos
    PaymentService --> Nacos
    ConfigService --> Nacos
```

## 2. 微服务架构图

### 2.1 服务间调用关系
展示各微服务之间的调用关系和数据流向。

```mermaid
graph LR
    %% 外部数据源
    ExtData[外部数据源] --> DocService[单据处理服务]
    
    %% 核心业务流程
    DocService --> |单据数据| SettlementService[供应商结算服务]
    SettlementService --> |结算单据| AccountingService[财务会计服务]
    AccountingService --> |会计凭证| PaymentService[自动化支付服务]
    
    %% 配置和认证服务
    AuthService[认证服务] --> |权限验证| DocService
    AuthService --> |权限验证| SettlementService
    AuthService --> |权限验证| AccountingService
    AuthService --> |权限验证| PaymentService
    
    ConfigService[配置管理服务] --> |业务配置| SettlementService
    ConfigService --> |会计规则| AccountingService
    ConfigService --> |支付配置| PaymentService
    
    %% 外部系统集成
    AccountingService --> |凭证同步| KingdeeERP[金蝶ERP]
    PaymentService --> |支付执行| BankAPI[银行接口]
    
    %% 消息队列通信
    DocService -.-> |异步消息| MQ[Apache Pulsar]
    MQ -.-> |消费消息| SettlementService
    MQ -.-> |消费消息| AccountingService
    MQ -.-> |消费消息| PaymentService
    
    %% 数据存储
    DocService --> DB1[(document_db)]
    SettlementService --> DB2[(settlement_db)]
    AccountingService --> DB3[(accounting_db)]
    PaymentService --> DB4[(payment_db)]
    ConfigService --> DB5[(config_db)]
    
    %% 缓存层
    DocService --> Cache[(Redis Cache)]
    SettlementService --> Cache
    AccountingService --> Cache
    PaymentService --> Cache
```

## 3. 数据流图

### 3.1 完整业务数据流
从单据处理到支付执行的完整数据流向图。

```mermaid
flowchart TD
    %% 数据输入
    A[外部业务系统] --> B[消息队列<br/>Apache Pulsar]
    
    %% 单据处理阶段
    B --> C[单据处理服务<br/>Document Processing]
    C --> D{单据验证}
    D -->|验证通过| E[单据持久化<br/>MySQL]
    D -->|验证失败| F[错误处理<br/>死信队列]
    
    %% 结算处理阶段
    E --> G[供应商结算服务<br/>Settlement Service]
    G --> H{账期检查}
    H -->|到期| I[生成结算单据]
    H -->|未到期| J[等待账期]
    
    I --> K[单据对账<br/>Reconciliation]
    K --> L{对账结果}
    L -->|对账成功| M[合并付款单]
    L -->|对账失败| N[异常处理]
    
    %% 会计处理阶段
    M --> O[财务会计服务<br/>Accounting Service]
    O --> P[单据锁定]
    P --> Q[生成会计凭证]
    Q --> R[金蝶ERP同步]
    R --> S{同步状态}
    S -->|同步成功| T[凭证确认]
    S -->|同步失败| U[重试机制]
    
    %% 支付处理阶段
    T --> V[自动化支付服务<br/>Payment Service]
    V --> W[支付池管理]
    W --> X[三级审批流程]
    X --> Y{审批结果}
    Y -->|审批通过| Z[银行支付执行]
    Y -->|审批拒绝| AA[支付取消]
    
    Z --> BB[银行接口调用]
    BB --> CC{支付结果}
    CC -->|支付成功| DD[状态更新<br/>支付完成]
    CC -->|支付失败| EE[异常处理<br/>重试或人工处理]
    
    %% 缓存和日志
    C --> FF[(Redis缓存)]
    G --> FF
    O --> FF
    V --> FF
    
    DD --> GG[审计日志<br/>ELK Stack]
    EE --> GG
    F --> GG
    N --> GG
    U --> GG
    
    %% 监控告警
    GG --> HH[监控告警<br/>Prometheus + Grafana]
```

## 4. 部署架构图

### 4.1 生产环境部署架构

```mermaid
graph TB
    %% 负载均衡层
    subgraph "负载均衡层"
        LB[Nginx负载均衡器<br/>SSL终端]
    end
    
    %% Kubernetes集群
    subgraph "Kubernetes集群"
        subgraph "Web层 Pod"
            WebPod1[Web应用 Pod-1]
            WebPod2[Web应用 Pod-2]
        end
        
        subgraph "网关层 Pod"
            GatewayPod1[Gateway Pod-1]
            GatewayPod2[Gateway Pod-2]
        end
        
        subgraph "微服务层 Pod"
            DocPod1[单据服务 Pod-1]
            DocPod2[单据服务 Pod-2]
            SettlePod1[结算服务 Pod-1]
            SettlePod2[结算服务 Pod-2]
            AcctPod1[会计服务 Pod-1]
            AcctPod2[会计服务 Pod-2]
            PayPod1[支付服务 Pod-1]
            PayPod2[支付服务 Pod-2]
        end
    end
    
    %% 数据层
    subgraph "数据存储层"
        subgraph "MySQL集群"
            MySQLMaster[(MySQL主库)]
            MySQLSlave1[(MySQL从库-1)]
            MySQLSlave2[(MySQL从库-2)]
        end
        
        subgraph "Redis集群"
            RedisNode1[(Redis节点-1)]
            RedisNode2[(Redis节点-2)]
            RedisNode3[(Redis节点-3)]
        end
        
        subgraph "Pulsar集群"
            PulsarBroker1[Pulsar Broker-1]
            PulsarBroker2[Pulsar Broker-2]
            PulsarBroker3[Pulsar Broker-3]
        end
    end
    
    %% 基础设施
    subgraph "基础设施"
        NacosCluster[Nacos集群<br/>服务发现+配置]
        MonitorStack[监控栈<br/>Prometheus+Grafana]
        LogStack[日志栈<br/>ELK Stack]
    end
    
    %% 连接关系
    LB --> WebPod1
    LB --> WebPod2
    WebPod1 --> GatewayPod1
    WebPod2 --> GatewayPod2
    
    GatewayPod1 --> DocPod1
    GatewayPod1 --> SettlePod1
    GatewayPod1 --> AcctPod1
    GatewayPod1 --> PayPod1
    
    GatewayPod2 --> DocPod2
    GatewayPod2 --> SettlePod2
    GatewayPod2 --> AcctPod2
    GatewayPod2 --> PayPod2
    
    DocPod1 --> MySQLMaster
    DocPod2 --> MySQLSlave1
    SettlePod1 --> MySQLMaster
    SettlePod2 --> MySQLSlave2
    
    DocPod1 --> RedisNode1
    DocPod2 --> RedisNode2
    SettlePod1 --> RedisNode1
    SettlePod2 --> RedisNode3
    
    DocPod1 --> PulsarBroker1
    DocPod2 --> PulsarBroker2
    SettlePod1 --> PulsarBroker1
    SettlePod2 --> PulsarBroker3
```

## 5. 网络架构图

### 5.1 网络拓扑和安全边界

```mermaid
graph TB
    %% 外部网络
    Internet[互联网] --> Firewall[防火墙<br/>WAF]
    
    %% DMZ区域
    subgraph "DMZ区域"
        Firewall --> LB[负载均衡器<br/>Nginx/HAProxy]
        LB --> WebServer[Web服务器<br/>静态资源]
    end
    
    %% 应用区域
    subgraph "应用网络区域"
        WebServer --> AppGateway[应用网关<br/>Spring Cloud Gateway]
        AppGateway --> MicroServices[微服务集群<br/>Kubernetes Pod网络]
    end
    
    %% 数据区域
    subgraph "数据网络区域"
        MicroServices --> DataLayer[数据存储层<br/>MySQL + Redis + Pulsar]
    end
    
    %% 管理区域
    subgraph "管理网络区域"
        AdminVPN[管理VPN] --> AdminTools[运维工具<br/>监控 + 日志 + 配置]
        AdminTools --> MicroServices
        AdminTools --> DataLayer
    end
    
    %% 外部集成
    subgraph "外部集成区域"
        MicroServices --> ExtAPI[外部API<br/>银行接口 + ERP系统]
    end
```

---

*本文档展示了系统的完整架构设计，为后续的详细实现提供了清晰的技术蓝图。*
