# 数据库架构设计 - 综合业务和财务中间件系统

## 1. 数据库整体架构

### 1.1 数据库分离策略
系统采用微服务架构，每个服务拥有独立的数据库，确保数据隔离和服务自治：

- **auth_db**: 认证服务数据库
- **document_db**: 单据处理服务数据库  
- **settlement_db**: 供应商结算服务数据库
- **accounting_db**: 财务会计服务数据库
- **payment_db**: 自动化支付服务数据库
- **config_db**: 配置管理服务数据库

### 1.2 数据库命名规范
- **表名**: 使用下划线分隔的小写字母，如 `supplier_info`
- **字段名**: 使用下划线分隔的小写字母，如 `create_time`
- **索引名**: `idx_表名_字段名`，如 `idx_supplier_info_supplier_code`
- **主键**: 统一使用 `id` 作为主键字段名
- **外键**: 使用 `关联表名_id`，如 `supplier_id`

## 2. 核心实体关系图（ER图）

### 2.1 整体实体关系图

```mermaid
erDiagram
    %% 用户和权限相关实体
    SYS_USER {
        bigint id PK
        varchar user_code UK
        varchar username
        varchar password
        varchar real_name
        varchar email
        varchar phone
        tinyint status
        datetime create_time
        datetime update_time
    }
    
    SYS_ROLE {
        bigint id PK
        varchar role_code UK
        varchar role_name
        varchar description
        tinyint status
        datetime create_time
    }
    
    SYS_USER_ROLE {
        bigint id PK
        bigint user_id FK
        bigint role_id FK
        datetime create_time
    }
    
    %% 供应商相关实体
    SUPPLIER_INFO {
        bigint id PK
        varchar supplier_code UK
        varchar supplier_name
        varchar contact_person
        varchar contact_phone
        varchar contact_email
        varchar bank_account
        varchar bank_name
        tinyint settlement_cycle
        tinyint status
        datetime create_time
        datetime update_time
    }
    
    %% 单据相关实体
    BUSINESS_DOCUMENT {
        bigint id PK
        varchar document_no UK
        varchar document_type
        bigint supplier_id FK
        bigint organization_id FK
        decimal amount
        varchar currency
        date business_date
        tinyint status
        varchar source_system
        text remark
        datetime create_time
        datetime update_time
    }
    
    DOCUMENT_DETAIL {
        bigint id PK
        bigint document_id FK
        varchar item_code
        varchar item_name
        decimal quantity
        decimal unit_price
        decimal amount
        varchar unit
        text remark
    }
    
    %% 结算相关实体
    SETTLEMENT_BATCH {
        bigint id PK
        varchar batch_no UK
        bigint supplier_id FK
        date settlement_start_date
        date settlement_end_date
        decimal total_amount
        tinyint status
        datetime create_time
        datetime update_time
    }
    
    SETTLEMENT_DOCUMENT {
        bigint id PK
        bigint batch_id FK
        bigint document_id FK
        decimal settlement_amount
        tinyint status
        datetime create_time
    }
    
    %% 支付相关实体
    PAYMENT_ORDER {
        bigint id PK
        varchar order_no UK
        bigint supplier_id FK
        bigint settlement_batch_id FK
        decimal payment_amount
        varchar currency
        varchar payment_method
        tinyint status
        varchar bank_account
        varchar bank_name
        datetime create_time
        datetime update_time
    }
    
    PAYMENT_APPROVAL {
        bigint id PK
        bigint payment_order_id FK
        bigint approver_id FK
        tinyint approval_level
        tinyint approval_result
        varchar approval_comment
        datetime approval_time
        datetime create_time
    }
    
    PAYMENT_EXECUTION {
        bigint id PK
        bigint payment_order_id FK
        varchar bank_transaction_no
        decimal executed_amount
        tinyint execution_status
        varchar execution_result
        datetime execution_time
        datetime create_time
    }
    
    %% 会计相关实体
    ACCOUNTING_VOUCHER {
        bigint id PK
        varchar voucher_no UK
        bigint settlement_batch_id FK
        varchar voucher_type
        date accounting_date
        decimal total_debit
        decimal total_credit
        tinyint status
        varchar erp_voucher_no
        datetime erp_sync_time
        datetime create_time
        datetime update_time
    }
    
    VOUCHER_ENTRY {
        bigint id PK
        bigint voucher_id FK
        varchar account_code
        varchar account_name
        decimal debit_amount
        decimal credit_amount
        varchar summary
        varchar cost_center
        varchar project_code
    }
    
    %% 配置相关实体
    SYSTEM_CONFIG {
        bigint id PK
        varchar config_key UK
        varchar config_value
        varchar config_desc
        varchar config_group
        tinyint status
        datetime create_time
        datetime update_time
    }
    
    ACCOUNTING_SUBJECT {
        bigint id PK
        varchar subject_code UK
        varchar subject_name
        varchar parent_code
        tinyint subject_level
        tinyint subject_type
        tinyint status
        datetime create_time
    }
    
    %% 审计日志实体
    AUDIT_LOG {
        bigint id PK
        varchar business_type
        varchar business_id
        varchar operation_type
        bigint operator_id FK
        varchar operator_name
        text operation_content
        varchar ip_address
        datetime operation_time
    }
    
    %% 实体关系定义
    SYS_USER ||--o{ SYS_USER_ROLE : "用户角色关联"
    SYS_ROLE ||--o{ SYS_USER_ROLE : "角色用户关联"
    
    SUPPLIER_INFO ||--o{ BUSINESS_DOCUMENT : "供应商单据"
    SUPPLIER_INFO ||--o{ SETTLEMENT_BATCH : "供应商结算"
    SUPPLIER_INFO ||--o{ PAYMENT_ORDER : "供应商支付"
    
    BUSINESS_DOCUMENT ||--o{ DOCUMENT_DETAIL : "单据明细"
    BUSINESS_DOCUMENT ||--o{ SETTLEMENT_DOCUMENT : "结算单据"
    
    SETTLEMENT_BATCH ||--o{ SETTLEMENT_DOCUMENT : "结算明细"
    SETTLEMENT_BATCH ||--o{ PAYMENT_ORDER : "结算支付"
    SETTLEMENT_BATCH ||--o{ ACCOUNTING_VOUCHER : "结算凭证"
    
    PAYMENT_ORDER ||--o{ PAYMENT_APPROVAL : "支付审批"
    PAYMENT_ORDER ||--o{ PAYMENT_EXECUTION : "支付执行"
    
    ACCOUNTING_VOUCHER ||--o{ VOUCHER_ENTRY : "凭证分录"
    
    SYS_USER ||--o{ PAYMENT_APPROVAL : "审批人"
    SYS_USER ||--o{ AUDIT_LOG : "操作人"
```

## 3. 详细表结构设计

### 3.1 认证服务数据库 (auth_db)

#### 3.1.1 用户表 (sys_user)
```sql
CREATE TABLE `sys_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_code` varchar(50) NOT NULL COMMENT '用户编码',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码(加密)',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_code` (`user_code`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统用户表';
```

#### 3.1.2 角色表 (sys_role)
```sql
CREATE TABLE `sys_role` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_code` varchar(50) NOT NULL COMMENT '角色编码',
  `role_name` varchar(50) NOT NULL COMMENT '角色名称',
  `description` varchar(200) DEFAULT NULL COMMENT '角色描述',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统角色表';
```

### 3.2 单据处理服务数据库 (document_db)

#### 3.2.1 业务单据表 (business_document)
```sql
CREATE TABLE `business_document` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `document_no` varchar(50) NOT NULL COMMENT '单据编号',
  `document_type` varchar(20) NOT NULL COMMENT '单据类型(PURCHASE:采购,SERVICE:服务,OTHER:其他)',
  `supplier_id` bigint NOT NULL COMMENT '供应商ID',
  `organization_id` bigint NOT NULL COMMENT '机构ID',
  `amount` decimal(15,2) NOT NULL COMMENT '单据金额',
  `currency` varchar(10) NOT NULL DEFAULT 'CNY' COMMENT '币种',
  `business_date` date NOT NULL COMMENT '业务日期',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:待处理,2:已处理,3:已结算,4:已锁定)',
  `source_system` varchar(50) DEFAULT NULL COMMENT '来源系统',
  `remark` text COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_document_no` (`document_no`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_organization_id` (`organization_id`),
  KEY `idx_business_date` (`business_date`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_composite_query` (`supplier_id`, `status`, `business_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='业务单据表'
PARTITION BY RANGE (YEAR(business_date)) (
  PARTITION p2023 VALUES LESS THAN (2024),
  PARTITION p2024 VALUES LESS THAN (2025),
  PARTITION p2025 VALUES LESS THAN (2026),
  PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

### 3.3 供应商结算服务数据库 (settlement_db)

#### 3.3.1 供应商信息表 (supplier_info)
```sql
CREATE TABLE `supplier_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `supplier_code` varchar(50) NOT NULL COMMENT '供应商编码',
  `supplier_name` varchar(100) NOT NULL COMMENT '供应商名称',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `contact_email` varchar(100) DEFAULT NULL COMMENT '联系邮箱',
  `bank_account` varchar(50) DEFAULT NULL COMMENT '银行账号',
  `bank_name` varchar(100) DEFAULT NULL COMMENT '开户银行',
  `settlement_cycle` tinyint NOT NULL DEFAULT '30' COMMENT '结算周期(天)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(0:禁用,1:启用)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_supplier_code` (`supplier_code`),
  KEY `idx_supplier_name` (`supplier_name`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='供应商信息表';
```

#### 3.3.2 结算批次表 (settlement_batch)
```sql
CREATE TABLE `settlement_batch` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_no` varchar(50) NOT NULL COMMENT '结算批次号',
  `supplier_id` bigint NOT NULL COMMENT '供应商ID',
  `settlement_start_date` date NOT NULL COMMENT '结算开始日期',
  `settlement_end_date` date NOT NULL COMMENT '结算结束日期',
  `total_amount` decimal(15,2) NOT NULL COMMENT '结算总金额',
  `document_count` int NOT NULL DEFAULT '0' COMMENT '单据数量',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:待确认,2:已确认,3:已生成付款单,4:已完成)',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_batch_no` (`batch_no`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_settlement_date` (`settlement_start_date`, `settlement_end_date`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='结算批次表';
```

## 4. 索引策略设计

### 4.1 主要索引策略

#### 4.1.1 单列索引
- **状态字段索引**: 所有包含status字段的表都创建状态索引，用于快速过滤有效数据
- **时间字段索引**: create_time, update_time, business_date等时间字段创建索引，支持时间范围查询
- **编码字段索引**: 各种业务编码字段(如supplier_code, document_no)创建唯一索引

#### 4.1.2 复合索引
- **业务查询索引**: 根据常用查询条件组合创建复合索引
- **外键关联索引**: 外键字段与状态、时间等字段组合创建复合索引
- **覆盖索引**: 对于频繁查询的字段组合，创建覆盖索引减少回表操作

### 4.2 分区策略

#### 4.2.1 时间分区
```sql
-- 业务单据表按年分区
ALTER TABLE business_document 
PARTITION BY RANGE (YEAR(business_date)) (
  PARTITION p2023 VALUES LESS THAN (2024),
  PARTITION p2024 VALUES LESS THAN (2025),
  PARTITION p2025 VALUES LESS THAN (2026),
  PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 审计日志表按月分区
ALTER TABLE audit_log 
PARTITION BY RANGE (TO_DAYS(operation_time)) (
  PARTITION p202401 VALUES LESS THAN (TO_DAYS('2024-02-01')),
  PARTITION p202402 VALUES LESS THAN (TO_DAYS('2024-03-01')),
  -- ... 其他月份分区
);
```

#### 4.2.2 哈希分区
```sql
-- 大表按机构ID哈希分区
ALTER TABLE business_document 
PARTITION BY HASH(organization_id) 
PARTITIONS 8;
```

### 4.3 支付服务数据库 (payment_db)

#### 4.3.1 支付订单表 (payment_order)
```sql
CREATE TABLE `payment_order` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_no` varchar(50) NOT NULL COMMENT '支付订单号',
  `supplier_id` bigint NOT NULL COMMENT '供应商ID',
  `settlement_batch_id` bigint DEFAULT NULL COMMENT '结算批次ID',
  `payment_amount` decimal(15,2) NOT NULL COMMENT '支付金额',
  `currency` varchar(10) NOT NULL DEFAULT 'CNY' COMMENT '币种',
  `payment_method` varchar(20) NOT NULL COMMENT '支付方式(BANK_TRANSFER:银行转账)',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:待审批,2:审批中,3:审批通过,4:审批拒绝,5:支付中,6:支付成功,7:支付失败)',
  `bank_account` varchar(50) DEFAULT NULL COMMENT '收款银行账号',
  `bank_name` varchar(100) DEFAULT NULL COMMENT '收款银行名称',
  `remark` text COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_supplier_id` (`supplier_id`),
  KEY `idx_settlement_batch_id` (`settlement_batch_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付订单表';
```

#### 4.3.2 支付审批表 (payment_approval)
```sql
CREATE TABLE `payment_approval` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `payment_order_id` bigint NOT NULL COMMENT '支付订单ID',
  `approver_id` bigint NOT NULL COMMENT '审批人ID',
  `approval_level` tinyint NOT NULL COMMENT '审批级别(1:一级,2:二级,3:三级)',
  `approval_result` tinyint DEFAULT NULL COMMENT '审批结果(1:通过,2:拒绝)',
  `approval_comment` varchar(500) DEFAULT NULL COMMENT '审批意见',
  `approval_time` datetime DEFAULT NULL COMMENT '审批时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_payment_order_id` (`payment_order_id`),
  KEY `idx_approver_id` (`approver_id`),
  KEY `idx_approval_level` (`approval_level`),
  KEY `idx_approval_time` (`approval_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付审批表';
```

### 4.4 会计服务数据库 (accounting_db)

#### 4.4.1 会计凭证表 (accounting_voucher)
```sql
CREATE TABLE `accounting_voucher` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `voucher_no` varchar(50) NOT NULL COMMENT '凭证号',
  `settlement_batch_id` bigint NOT NULL COMMENT '结算批次ID',
  `voucher_type` varchar(20) NOT NULL DEFAULT 'SETTLEMENT' COMMENT '凭证类型',
  `accounting_date` date NOT NULL COMMENT '会计日期',
  `total_debit` decimal(15,2) NOT NULL COMMENT '借方合计',
  `total_credit` decimal(15,2) NOT NULL COMMENT '贷方合计',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态(1:草稿,2:已生成,3:已同步ERP,4:同步失败)',
  `erp_voucher_no` varchar(50) DEFAULT NULL COMMENT 'ERP凭证号',
  `erp_sync_time` datetime DEFAULT NULL COMMENT 'ERP同步时间',
  `sync_error_msg` text COMMENT '同步错误信息',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_voucher_no` (`voucher_no`),
  KEY `idx_settlement_batch_id` (`settlement_batch_id`),
  KEY `idx_accounting_date` (`accounting_date`),
  KEY `idx_status` (`status`),
  KEY `idx_erp_sync_time` (`erp_sync_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会计凭证表';
```

## 5. 数据字典

### 5.1 状态枚举定义

#### 5.1.1 用户状态 (sys_user.status)
- `0`: 禁用 - 用户账号被禁用，无法登录系统
- `1`: 启用 - 用户账号正常，可以登录系统

#### 5.1.2 单据状态 (business_document.status)
- `1`: 待处理 - 单据已接收，等待处理
- `2`: 已处理 - 单据处理完成，等待结算
- `3`: 已结算 - 单据已纳入结算批次
- `4`: 已锁定 - 单据已锁定，准备生成会计凭证

#### 5.1.3 结算状态 (settlement_batch.status)
- `1`: 待确认 - 结算批次已生成，等待确认
- `2`: 已确认 - 结算批次已确认，等待生成付款单
- `3`: 已生成付款单 - 已生成付款单，等待支付
- `4`: 已完成 - 结算流程完成

#### 5.1.4 支付状态 (payment_order.status)
- `1`: 待审批 - 支付订单已创建，等待审批
- `2`: 审批中 - 支付订单正在审批流程中
- `3`: 审批通过 - 支付订单审批通过，等待执行支付
- `4`: 审批拒绝 - 支付订单审批被拒绝
- `5`: 支付中 - 正在执行银行支付
- `6`: 支付成功 - 银行支付执行成功
- `7`: 支付失败 - 银行支付执行失败

#### 5.1.5 会计凭证状态 (accounting_voucher.status)
- `1`: 草稿 - 凭证处于草稿状态
- `2`: 已生成 - 凭证已生成，等待同步ERP
- `3`: 已同步ERP - 凭证已成功同步到ERP系统
- `4`: 同步失败 - 凭证同步ERP失败

### 5.2 业务规则说明

#### 5.2.1 结算周期规则
- 供应商结算周期以天为单位，默认30天
- 系统每日检查到期的供应商进行自动结算
- 结算日期范围：从上次结算结束日期+1天到当前日期-结算周期天数

#### 5.2.2 支付审批规则
- 支付金额 ≤ 10万：需要一级审批
- 支付金额 10万 < 金额 ≤ 50万：需要二级审批
- 支付金额 > 50万：需要三级审批
- 审批必须按级别顺序进行，不可跳级

#### 5.2.3 会计凭证生成规则
- 每个结算批次生成一张会计凭证
- 借方：应付账款 - 供应商
- 贷方：银行存款 - 对应银行账户
- 凭证摘要：供应商结算 + 结算批次号

### 5.3 数据完整性约束

#### 5.3.1 外键约束
- 所有外键字段必须引用对应主表的有效记录
- 删除主表记录前必须先删除或更新相关的从表记录

#### 5.3.2 业务约束
- 单据金额必须大于0
- 结算批次的开始日期必须小于等于结束日期
- 支付金额必须等于对应结算批次的总金额
- 会计凭证的借方合计必须等于贷方合计

#### 5.3.3 唯一性约束
- 单据编号在系统内全局唯一
- 结算批次号全局唯一
- 支付订单号全局唯一
- 会计凭证号全局唯一

## 6. 数据库优化建议

### 6.1 查询优化
- 对于大表查询，优先使用分区裁剪
- 避免在WHERE条件中使用函数，影响索引使用
- 合理使用LIMIT限制返回结果集大小
- 对于复杂查询，考虑使用物化视图

### 6.2 存储优化
- 定期清理历史数据，保持表大小合理
- 对于只读的历史数据，考虑使用归档表
- 合理设置表的存储引擎和字符集
- 定期分析表统计信息，优化查询计划

### 6.3 维护策略
- 定期备份重要业务数据
- 监控数据库性能指标
- 定期检查和优化慢查询
- 建立数据库监控和告警机制

---

*本文档提供了完整的数据库架构设计，包括实体关系、表结构、索引策略、分区方案和数据字典，为系统实现提供了详细的数据层设计指导。*
