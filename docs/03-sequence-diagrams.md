# 核心业务流程时序图 - 综合业务和财务中间件系统

## 1. 支付流程时序图

### 1.1 完整支付流程
展示从支付申请提交到银行执行的完整三级审批流程。

```mermaid
sequenceDiagram
    participant User as 用户/申请人
    participant WebApp as Web前端
    participant Gateway as API网关
    participant AuthSvc as 认证服务
    participant PaymentSvc as 支付服务
    participant WorkflowEngine as 工作流引擎
    participant Approver1 as 一级审批人
    participant Approver2 as 二级审批人
    participant Approver3 as 三级审批人
    participant BankAPI as 银行接口
    participant NotificationSvc as 通知服务
    participant AuditSvc as 审计服务
    participant Redis as Redis缓存
    participant MySQL as 数据库

    %% 支付申请提交
    User->>WebApp: 提交支付申请
    WebApp->>Gateway: POST /api/payment/apply
    Gateway->>AuthSvc: 验证用户权限
    AuthSvc-->>Gateway: 权限验证通过
    Gateway->>PaymentSvc: 创建支付申请

    %% 支付申请验证和保存
    PaymentSvc->>PaymentSvc: 验证申请数据
    PaymentSvc->>MySQL: 保存支付申请
    PaymentSvc->>Redis: 缓存申请信息
    PaymentSvc->>WorkflowEngine: 启动审批流程
    PaymentSvc-->>Gateway: 申请提交成功
    Gateway-->>WebApp: 返回申请ID
    WebApp-->>User: 显示申请成功

    %% 一级审批流程
    WorkflowEngine->>NotificationSvc: 发送审批通知
    NotificationSvc->>Approver1: 审批通知(邮件/短信)
    
    Note over Approver1: 审批人登录系统查看申请
    Approver1->>WebApp: 查看待审批申请
    WebApp->>PaymentSvc: 获取申请详情
    PaymentSvc->>MySQL: 查询申请信息
    PaymentSvc-->>WebApp: 返回申请详情
    
    alt 一级审批通过
        Approver1->>WebApp: 提交审批(通过)
        WebApp->>PaymentSvc: 更新审批状态
        PaymentSvc->>MySQL: 更新审批记录
        PaymentSvc->>AuditSvc: 记录审批日志
        PaymentSvc->>WorkflowEngine: 流转到二级审批
        
        %% 二级审批流程
        WorkflowEngine->>NotificationSvc: 发送二级审批通知
        NotificationSvc->>Approver2: 审批通知
        
        Approver2->>WebApp: 查看待审批申请
        alt 二级审批通过
            Approver2->>WebApp: 提交审批(通过)
            PaymentSvc->>WorkflowEngine: 流转到三级审批
            
            %% 三级审批流程
            WorkflowEngine->>NotificationSvc: 发送三级审批通知
            NotificationSvc->>Approver3: 审批通知
            
            Approver3->>WebApp: 查看待审批申请
            alt 三级审批通过
                Approver3->>WebApp: 提交审批(通过)
                PaymentSvc->>MySQL: 更新为审批完成状态
                PaymentSvc->>PaymentSvc: 执行银行支付
                
                %% 银行支付执行
                PaymentSvc->>BankAPI: 调用银行支付接口
                BankAPI-->>PaymentSvc: 返回支付结果
                
                alt 银行支付成功
                    PaymentSvc->>MySQL: 更新支付成功状态
                    PaymentSvc->>AuditSvc: 记录支付成功日志
                    PaymentSvc->>NotificationSvc: 发送支付成功通知
                    NotificationSvc->>User: 支付成功通知
                else 银行支付失败
                    PaymentSvc->>MySQL: 更新支付失败状态
                    PaymentSvc->>AuditSvc: 记录支付失败日志
                    PaymentSvc->>NotificationSvc: 发送支付失败通知
                    NotificationSvc->>User: 支付失败通知
                end
                
            else 三级审批拒绝
                Approver3->>WebApp: 提交审批(拒绝)
                PaymentSvc->>MySQL: 更新为审批拒绝状态
                PaymentSvc->>NotificationSvc: 发送拒绝通知
                NotificationSvc->>User: 审批拒绝通知
            end
            
        else 二级审批拒绝
            Approver2->>WebApp: 提交审批(拒绝)
            PaymentSvc->>MySQL: 更新为审批拒绝状态
            PaymentSvc->>NotificationSvc: 发送拒绝通知
            NotificationSvc->>User: 审批拒绝通知
        end
        
    else 一级审批拒绝
        Approver1->>WebApp: 提交审批(拒绝)
        PaymentSvc->>MySQL: 更新为审批拒绝状态
        PaymentSvc->>AuditSvc: 记录审批拒绝日志
        PaymentSvc->>NotificationSvc: 发送拒绝通知
        NotificationSvc->>User: 审批拒绝通知
    end
```

## 2. 供应商结算流程时序图

### 2.1 供应商结算完整流程
展示从单据收集到付款单生成的完整结算流程。

```mermaid
sequenceDiagram
    participant Scheduler as 定时调度器
    participant SettlementSvc as 结算服务
    participant DocSvc as 单据服务
    participant ConfigSvc as 配置服务
    participant MySQL as 数据库
    participant Redis as Redis缓存
    participant Pulsar as 消息队列
    participant PaymentSvc as 支付服务
    participant NotificationSvc as 通知服务
    participant AuditSvc as 审计服务

    %% 定时触发结算任务
    Scheduler->>SettlementSvc: 触发结算任务(定时)
    SettlementSvc->>ConfigSvc: 获取结算配置
    ConfigSvc->>MySQL: 查询结算规则配置
    ConfigSvc-->>SettlementSvc: 返回配置信息
    
    %% 获取待结算供应商列表
    SettlementSvc->>MySQL: 查询待结算供应商
    SettlementSvc->>Redis: 检查结算锁状态
    
    loop 遍历每个供应商
        SettlementSvc->>Redis: 设置供应商结算锁
        
        %% 账期检查
        SettlementSvc->>SettlementSvc: 检查供应商账期
        alt 账期已到
            %% 收集单据数据
            SettlementSvc->>DocSvc: 获取供应商单据数据
            DocSvc->>MySQL: 查询单据信息
            DocSvc-->>SettlementSvc: 返回单据列表
            
            %% 单据验证和对账
            SettlementSvc->>SettlementSvc: 验证单据完整性
            SettlementSvc->>SettlementSvc: 执行单据对账逻辑
            
            alt 对账成功
                %% 生成结算单据
                SettlementSvc->>MySQL: 创建结算单据
                SettlementSvc->>SettlementSvc: 计算结算金额
                SettlementSvc->>SettlementSvc: 应用结算规则
                
                %% 合并生成付款单
                SettlementSvc->>SettlementSvc: 合并同供应商单据
                SettlementSvc->>MySQL: 生成付款单
                SettlementSvc->>AuditSvc: 记录结算日志
                
                %% 发送到支付服务
                SettlementSvc->>Pulsar: 发送付款单消息
                Pulsar->>PaymentSvc: 消费付款单消息
                PaymentSvc->>MySQL: 创建支付任务
                
                %% 通知相关人员
                SettlementSvc->>NotificationSvc: 发送结算完成通知
                NotificationSvc->>NotificationSvc: 通知财务人员
                
            else 对账失败
                SettlementSvc->>MySQL: 记录对账异常
                SettlementSvc->>AuditSvc: 记录异常日志
                SettlementSvc->>NotificationSvc: 发送异常通知
                NotificationSvc->>NotificationSvc: 通知相关人员处理
            end
            
        else 账期未到
            SettlementSvc->>SettlementSvc: 跳过当前供应商
            Note over SettlementSvc: 等待下次调度
        end
        
        SettlementSvc->>Redis: 释放供应商结算锁
    end
    
    %% 结算任务完成
    SettlementSvc->>AuditSvc: 记录结算任务完成日志
    SettlementSvc->>Redis: 更新结算状态缓存
```

## 3. 财务会计工作流时序图

### 3.1 财务会计处理完整流程
展示从单据锁定到ERP同步的完整会计处理流程。

```mermaid
sequenceDiagram
    participant Scheduler as 定时调度器
    participant AccountingSvc as 会计服务
    participant DocSvc as 单据服务
    participant ConfigSvc as 配置服务
    participant VoucherEngine as 凭证生成引擎
    participant KingdeeAPI as 金蝶ERP接口
    participant MySQL as 数据库
    participant Redis as Redis缓存
    participant Pulsar as 消息队列
    participant NotificationSvc as 通知服务
    participant AuditSvc as 审计服务

    %% 定时触发会计处理任务
    Scheduler->>AccountingSvc: 触发会计处理任务
    AccountingSvc->>ConfigSvc: 获取会计规则配置
    ConfigSvc->>MySQL: 查询会计科目配置
    ConfigSvc-->>AccountingSvc: 返回会计规则
    
    %% 获取待处理单据
    AccountingSvc->>DocSvc: 获取待处理单据列表
    DocSvc->>MySQL: 查询已结算未入账单据
    DocSvc-->>AccountingSvc: 返回单据列表
    
    loop 处理每个单据
        %% 单据锁定
        AccountingSvc->>Redis: 尝试获取单据锁
        alt 获取锁成功
            AccountingSvc->>MySQL: 更新单据状态为"处理中"
            AccountingSvc->>AuditSvc: 记录单据锁定日志
            
            %% 验证单据数据
            AccountingSvc->>AccountingSvc: 验证单据完整性
            AccountingSvc->>AccountingSvc: 检查会计期间
            
            alt 单据验证通过
                %% 生成会计凭证
                AccountingSvc->>VoucherEngine: 调用凭证生成引擎
                VoucherEngine->>ConfigSvc: 获取科目映射规则
                VoucherEngine->>VoucherEngine: 应用会计规则
                VoucherEngine->>VoucherEngine: 生成借贷分录
                VoucherEngine-->>AccountingSvc: 返回会计凭证
                
                %% 保存凭证到本地数据库
                AccountingSvc->>MySQL: 保存会计凭证
                AccountingSvc->>AuditSvc: 记录凭证生成日志
                
                %% 同步到金蝶ERP
                AccountingSvc->>KingdeeAPI: 调用ERP凭证接口
                KingdeeAPI-->>AccountingSvc: 返回同步结果
                
                alt ERP同步成功
                    AccountingSvc->>MySQL: 更新同步成功状态
                    AccountingSvc->>MySQL: 记录ERP凭证号
                    AccountingSvc->>AuditSvc: 记录同步成功日志
                    
                    %% 发送成功通知
                    AccountingSvc->>NotificationSvc: 发送处理成功通知
                    
                else ERP同步失败
                    AccountingSvc->>MySQL: 更新同步失败状态
                    AccountingSvc->>AuditSvc: 记录同步失败日志
                    
                    %% 重试机制
                    AccountingSvc->>AccountingSvc: 检查重试次数
                    alt 未达到最大重试次数
                        AccountingSvc->>Pulsar: 发送重试消息
                        Note over AccountingSvc: 延迟重试
                    else 达到最大重试次数
                        AccountingSvc->>MySQL: 标记为人工处理
                        AccountingSvc->>NotificationSvc: 发送异常通知
                        NotificationSvc->>NotificationSvc: 通知财务人员处理
                    end
                end
                
            else 单据验证失败
                AccountingSvc->>MySQL: 更新单据状态为"验证失败"
                AccountingSvc->>AuditSvc: 记录验证失败日志
                AccountingSvc->>NotificationSvc: 发送验证失败通知
            end
            
            %% 释放单据锁
            AccountingSvc->>Redis: 释放单据锁
            AccountingSvc->>MySQL: 更新单据处理完成状态
            
        else 获取锁失败
            Note over AccountingSvc: 单据正在被其他实例处理
            AccountingSvc->>AccountingSvc: 跳过当前单据
        end
    end
    
    %% 会计处理任务完成
    AccountingSvc->>AuditSvc: 记录任务完成日志
    AccountingSvc->>Redis: 更新处理状态统计
    AccountingSvc->>NotificationSvc: 发送任务完成报告
```

---

*以上时序图详细展示了系统核心业务流程的完整交互过程，包含了异常处理和关键业务规则验证点。*
