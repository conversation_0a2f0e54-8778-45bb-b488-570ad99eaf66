version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: finance-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: finance_db
      MYSQL_USER: finance_user
      MYSQL_PASSWORD: finance_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - finance-network

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: finance-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - finance-network

  # Nacos服务发现和配置中心
  nacos:
    image: nacos/nacos-server:v2.2.0
    container_name: finance-nacos
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_DB_NAME: nacos_config
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: root123
      MYSQL_SERVICE_DB_PARAM: characterEncoding=utf8&connectTimeout=1000&socketTimeout=3000&autoReconnect=true&useSSL=false&allowPublicKeyRetrieval=true
    ports:
      - "8848:8848"
      - "9848:9848"
    depends_on:
      - mysql
    networks:
      - finance-network

  # Apache Pulsar消息队列
  pulsar:
    image: apachepulsar/pulsar:3.1.0
    container_name: finance-pulsar
    command: bin/pulsar standalone
    ports:
      - "6650:6650"
      - "8080:8080"
    volumes:
      - pulsar_data:/pulsar/data
    networks:
      - finance-network

  # API网关
  gateway-service:
    build:
      context: ./services/gateway-service
      dockerfile: Dockerfile
    container_name: finance-gateway
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
    depends_on:
      - nacos
      - redis
    networks:
      - finance-network

  # 认证服务
  auth-service:
    build:
      context: ./services/auth-service
      dockerfile: Dockerfile
    container_name: finance-auth
    ports:
      - "8081:8081"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      REDIS_HOST: redis
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - finance-network

  # 单据处理服务
  document-service:
    build:
      context: ./services/document-service
      dockerfile: Dockerfile
    container_name: finance-document
    ports:
      - "8082:8082"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      REDIS_HOST: redis
      PULSAR_HOST: pulsar
    depends_on:
      - mysql
      - redis
      - nacos
      - pulsar
    networks:
      - finance-network

  # 供应商结算服务
  settlement-service:
    build:
      context: ./services/settlement-service
      dockerfile: Dockerfile
    container_name: finance-settlement
    ports:
      - "8083:8083"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      REDIS_HOST: redis
      PULSAR_HOST: pulsar
    depends_on:
      - mysql
      - redis
      - nacos
      - pulsar
    networks:
      - finance-network

  # 支付服务
  payment-service:
    build:
      context: ./services/payment-service
      dockerfile: Dockerfile
    container_name: finance-payment
    ports:
      - "8084:8084"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      REDIS_HOST: redis
      PULSAR_HOST: pulsar
    depends_on:
      - mysql
      - redis
      - nacos
      - pulsar
    networks:
      - finance-network

  # 财务会计服务
  accounting-service:
    build:
      context: ./services/accounting-service
      dockerfile: Dockerfile
    container_name: finance-accounting
    ports:
      - "8085:8085"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      NACOS_SERVER_ADDR: nacos:8848
      MYSQL_HOST: mysql
      REDIS_HOST: redis
      PULSAR_HOST: pulsar
    depends_on:
      - mysql
      - redis
      - nacos
      - pulsar
    networks:
      - finance-network

  # 前端应用
  web-app:
    build:
      context: ./web-app
      dockerfile: Dockerfile
    container_name: finance-web
    ports:
      - "3000:3000"
    environment:
      REACT_APP_API_BASE_URL: http://localhost:8080
    depends_on:
      - gateway-service
    networks:
      - finance-network

volumes:
  mysql_data:
  redis_data:
  pulsar_data:

networks:
  finance-network:
    driver: bridge
