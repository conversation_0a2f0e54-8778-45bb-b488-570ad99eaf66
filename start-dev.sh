#!/bin/bash

echo "=== 综合业务和财务中间件系统 - 开发环境启动脚本 ==="

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "错误: Docker未运行，请先启动Docker"
    exit 1
fi

# 检查docker-compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "错误: docker-compose未安装"
    exit 1
fi

echo "1. 清理旧的容器和镜像..."
docker-compose down -v
docker system prune -f

echo "2. 构建Maven项目..."
if command -v mvn &> /dev/null; then
    mvn clean package -DskipTests
else
    echo "警告: Maven未安装，跳过构建步骤"
    echo "请手动执行: mvn clean package -DskipTests"
fi

echo "3. 启动基础设施服务 (MySQL, Redis, Nacos, Pulsar)..."
docker-compose up -d mysql redis nacos pulsar

echo "4. 等待基础设施服务启动完成..."
sleep 30

echo "5. 检查基础设施服务状态..."
echo "MySQL状态:"
docker-compose exec mysql mysqladmin ping -h localhost -u root -proot123

echo "Redis状态:"
docker-compose exec redis redis-cli ping

echo "Nacos状态:"
curl -s http://localhost:8848/nacos/actuator/health || echo "Nacos未就绪"

echo "Pulsar状态:"
curl -s http://localhost:8080/admin/v2/clusters || echo "Pulsar未就绪"

echo "6. 启动应用服务..."
# 暂时只启动认证服务进行测试
docker-compose up -d auth-service

echo "7. 等待应用服务启动..."
sleep 20

echo "8. 检查应用服务状态..."
echo "认证服务状态:"
curl -s http://localhost:8081/actuator/health || echo "认证服务未就绪"

echo ""
echo "=== 启动完成 ==="
echo "服务访问地址:"
echo "- Nacos控制台: http://localhost:8848/nacos (用户名/密码: nacos/nacos)"
echo "- 认证服务API文档: http://localhost:8081/swagger-ui/index.html"
echo "- 认证服务健康检查: http://localhost:8081/actuator/health"
echo "- MySQL连接: localhost:3306 (用户名/密码: finance_user/finance_pass)"
echo "- Redis连接: localhost:6379"
echo ""
echo "测试登录API:"
echo "curl -X POST http://localhost:8081/auth/login \\"
echo "  -H 'Content-Type: application/json' \\"
echo "  -d '{\"username\":\"admin\",\"password\":\"123456\"}'"
echo ""
echo "查看日志: docker-compose logs -f [service-name]"
echo "停止服务: docker-compose down"
